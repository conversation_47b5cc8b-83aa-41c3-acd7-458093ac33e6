package com.digiwin.escloud.aiochat.issue.controller;

import com.digiwin.escloud.aiochat.issue.dao.IssueMapper;
import com.digiwin.escloud.aiochat.issue.service.IssueDescCallAIAgentService;
import com.digiwin.escloud.aiochat.model.IssueDescRequest;
import com.digiwin.escloud.common.model.ResponseBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.List;

import static com.digiwin.escloud.aiochat.aichat.utils.HtmlUtil.removeHtmlTags;


@RestController
@RequestMapping("/issue/chat")
@Slf4j
public class IssueDescController {

    @Autowired
    private IssueDescCallAIAgentService issueDescCallAIAgentService;

    @Autowired
    private IssueMapper issueMapper;

    @PostMapping(value = "/getIssueDescByFlow", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<Flux<ServerSentEvent<Object>>> getIssueDescByFlow(@RequestBody IssueDescRequest request) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-Accel-Buffering", "no");
        headers.add("Content-Type", "text/event-stream");
        headers.add("Cache-Control", "no-cache");
        return ResponseEntity.ok()
                .headers(headers)
                .body(issueDescCallAIAgentService.getIssueDescByFlow(request));
    }

    @PostMapping(value = "/getIssueDescForReport")
    public ResponseBase getIssueDescForReport(@RequestBody IssueDescRequest request) {
        return issueDescCallAIAgentService.getIssueDescForReport(request);
    }

    @PostMapping("/getIssueDescListSize")
    public ResponseBase getIssueDescListSize(@RequestBody IssueDescRequest request) {
        request.setDb("escloud-db");
        List<String> descList = issueMapper.selectIssueDesc(request);
        String issueDescString = "";
        if (descList != null && !descList.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            int index = 1;
            for (String desc : descList) {
                if (desc != null && !desc.trim().isEmpty()) {
                    sb.append(" ").append(removeHtmlTags(desc.trim())).append("。");
                }
            }
            issueDescString = sb.toString();
            log.info("question size: {}" , issueDescString.length());
            return ResponseBase.ok(descList.size());
        }
        return ResponseBase.ok(0);
    }
}
