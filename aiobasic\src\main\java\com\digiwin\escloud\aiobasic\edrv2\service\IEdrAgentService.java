package com.digiwin.escloud.aiobasic.edrv2.service;

import com.digiwin.escloud.aiobasic.edrv2.model.*;
import com.digiwin.escloud.aiobasic.report.model.EdrAgentField;
import com.digiwin.escloud.common.response.BaseResponse;
import com.fasterxml.jackson.core.JsonProcessingException;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

public interface IEdrAgentService {
    BaseResponse getAgentList(List<String> eidList, EdrAgentParam edrAgentParam);
    BaseResponse saveAgent(List<EdrAgentField> edrAgentFieldRequest);
    BaseResponse getOsTotalAmount(String eid, Date timeFrom, Date timeTo);
    BaseResponse getConnectivityTotalAmount(Long eid, Date timeFrom, Date timeTo);
    BaseResponse getHealthTotalAmount(Long eid, Date timeFrom, Date timeTo);
    BaseResponse getAgentConnectivityTotalAmount(Long eid, Date timeFrom, Date timeTo);
    BaseResponse enableOrDisableAgent(String state, EdrAgentEnableParam edrAgentEnableParam);
    BaseResponse removeAgent(EdrAgentDeleteParam param);
    BaseResponse getOsNameList(Long eid);
    BaseResponse syncAgent(String eid, List<String> siteIds) throws Exception;
    BaseResponse checkAgentStatus(EdrAgentCheckStatusParam param);
    void exportAgentList(HttpServletResponse response, String eid, EdrAgentExportParam param);
    BaseResponse networkIsolationOrConnect(EdrNetworkIsolationParam param);
    BaseResponse scan(EdrScan edrScan) throws Exception;
    BaseResponse sendRemoveIdentity(EdrIdentitySend edrIdentitySend) throws Exception;
    BaseResponse uninstall(EdrUninstall edrUninstall) throws Exception;
    List<EdrAgentCheckStatus> getEdrAgentCheckStatusList(EdrAgentCheckStatusParam param);
    BaseResponse saveIsWarning(EdrAgentIsWarningParam param);
    BaseResponse selectIsWarning(String siteId, Boolean isWarning);
}
