<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="escloud.issuesyncmapper">
    <resultMap id="issueresultmap" type="com.digiwin.escloud.issueservice.model.Issue">
    </resultMap>

    <resultMap id="issueselectmap" type="com.digiwin.escloud.issueservice.model.Issue">
        <result property="issueId" column="issueId"/>
        <result property="crmId" column="crmId"/>
        <result property="serviceCode" column="serviceCode"/>
        <result property="productCode" column="productCode"/>
        <result property="productVersion" column="productVersion"/>
        <result property="userId" column="userId"/>
        <result property="userContactId" column="userContactId"/>
        <result property="issueDescription" column="issueDescription"/>
        <result property="issueStack" column="issueStack"/>
        <result property="submitWay" column="submitWay"/>
        <result property="issueStatus" column="issueStatus"/>
        <result property="submitTime" column="submitTime"/>
        <result property="serviceStaff" column="serviceStaff"/>
        <result property="department" column="department"/>
        <result property="customerName" column="customerName"/>
        <result property="issueStatus" column="issueStatus"/>
        <result property="syncStatus" column="syncStatus"/>
        <result property="issueCloseInform" column="issueCloseInform"/>
        <result property="site" column="site"/>
        <result property="mainCharge" column="mainCharge"/>
        <result property="mainCharge_workNo" column="mainCharge_workNo"/>
        <result property="mainCharge_DepartmentCode" column="mainCharge_DepartmentCode"/>
        <result property="window_department" column="window_department"/>
        <result property="customerCode" column="customerCode"/>
        <result property="question_title" column="question_title"/>
        <result property="userId_name" column="userId_name"/>
        <result property="userId_departmentcode" column="userId_departmentcode"/>
        <result property="userId_mail" column="userId_mail"/>
        <result property="emergency" column="emergency"/>
        <result property="serviceRegion" column="serviceRegion"/>
        <result property="userLanguage" column="userLanguage"/>

        <association property="userContact" columnPrefix="userContact_"
                     resultMap="userContactselectmap" />
        <association property="issueProgresses" columnPrefix="progress_"
                     resultMap="issueprogressmap" />
        <association property="issueCasedetail" columnPrefix="detail_"
                     resultMap="issuecasedetailmap" />
        <association property="issueKbshare" columnPrefix="kbshare_"
                     resultMap="issuekbsharemap" />
    </resultMap>
    <resultMap id="userContactselectmap" type="com.digiwin.escloud.issueservice.model.UserContact">
        <result property="userId" column="userId"/>
        <result property="name" column="name"/>
        <result property="email" column="email"/>
        <result property="phone01" column="phone01"/>
        <result property="phone02" column="phone02"/>
        <result property="phone03" column="phone03"/>
        <result property="qq" column="qq"/>
    </resultMap>
    <resultMap id="issueprogressmap" type="com.digiwin.escloud.issueservice.model.IssueProgress">
        <result property="id" column="id"/>
        <result property="issueId" column="issueId"/>
        <result property="sequeceNum" column="sequeceNum"/>
        <result property="processType" column="processType"/>
        <result property="processTime" column="processTime"/>
        <result property="processor" column="processor"/>
        <result property="description" column="description"/>
        <result property="replyType" column="replyType"/>
        <result property="processHours" column="processHours"/>
        <result property="crmId" column="crmId"/>
        <result property="workno" column="workno"/>
        <result property="closeServiceStaff" column="closeServiceStaff"/>
        <result property="closeDepartment" column="closeDepartment"/>
        <result property="CurrentStatus" column="currentStatus"/>
        <result property="syncStatus" column="syncStatus"/>
        <result property="processorName" column="processorName"/>
        <result property="isOpenToSubmit" column="isOpenToSubmit"/>
        <result property="crm_BR002" column="crm_BR002"/>
        <result property="crm_BR003" column="crm_BR003"/>
    </resultMap>
    <resultMap id="issuecasedetailmap" type="com.digiwin.escloud.issueservice.model.IssueCasedetail">
        <result property="issueId" column="issueId"/>
        <result property="issueClassification" column="issueClassification"/>
        <result property="erpSystemCode" column="erpSystemCode"/>
        <result property="programCode" column="programCode"/>
        <result property="programVersion" column="programVersion"/>
        <result property="programVersion" column="programVersion"/>
        <result property="syncStatus" column="syncStatus"/>
        <result property="totalWorkHours" column="totalWorkHours"/>
        <result property="requNo" column="requNo"/>
        <result property="requSeq" column="requSeq"/>
        <result property="expectedCompletionDate" column="expectedCompletionDate"/>
        <result property="estimatedWorkHours" column="estimatedWorkHours"/>
        <result property="requExpectCompletionDate" column="requExpectCompletionDate"/>
        <result property="requPlanCompletionDate" column="requPlanCompletionDate"/>
        <result property="requActualCompletionDate" column="requActualCompletionDate"/>
        <result property="chatfileHelp" column="chatfileHelp"/>
    </resultMap>

    <resultMap id="customizedIssueselectmap" type="com.digiwin.escloud.issueservice.model.CustomizedIssue">
        <result property="requId" column="requId"/>
        <result property="issueCrmId" column="issueCrmId"/>
        <result property="crmId" column="crmId"/>
        <result property="customerServiceCode" column="customerServiceCode"/>
        <result property="productCode" column="productCode"/>
        <result property="isFromCustomer" column="isFromCustomer"/>
        <result property="erpSystemCode" column="erpSystemCode"/>
        <result property="programCode" column="programCode"/>
        <result property="type" column="type"/>
        <result property="difficultyLevel" column="difficultyLevel"/>
        <result property="priority" column="priority"/>
        <result property="title" column="title"/>
        <result property="description" column="description"/>
        <result property="submitWay" column="submitWay"/>
        <result property="status" column="status"/>
        <result property="submitTime" column="submitTime"/>
        <result property="modifyTime" column="modifyTime"/>
        <result property="userId" column="userId"/>
        <result property="userId_name" column="userId_name"/>
        <result property="userId_departmentcode" column="userId_departmentcode"/>
        <result property="submitter" column="submitter"/>
        <result property="handlerId" column="handlerId"/>
        <result property="plannedCompletionDate" column="plannedCompletionDate"/>
        <result property="completionDate" column="completionDate"/>

        <result property="isCheckPassWithoutIssue" column="isCheckPassWithoutIssue"/>
        <result property="isAcceptWithoutIssue" column="isAcceptWithoutIssue"/>
        <result property="isSdSpec" column="isSdSpec"/>
        <result property="isTestSpec" column="isTestSpec"/>
        <result property="sdUserId" column="sdUserId"/>
        <result property="sdUserId_name" column="sdUserId_name"/>
        <result property="sdUserId_departmentcode" column="sdUserId_departmentcode"/>
        <result property="prUserId" column="prUserId"/>
        <result property="prUserId_name" column="prUserId_name"/>
        <result property="prUserId_departmentcode" column="prUserId_departmentcode"/>
        <result property="testUserId" column="testUserId"/>
        <result property="testUserId_name" column="testUserId_name"/>
        <result property="testUserId_departmentcode" column="testUserId_departmentcode"/>
        <result property="teamCheckoutUserId" column="teamCheckoutUserId"/>
        <result property="teamCheckoutUserId_name" column="teamCheckoutUserId_name"/>
        <result property="teamCheckoutUserId_departmentcode" column="teamCheckoutUserId_departmentcode"/>
        <result property="customerCheckoutUserId" column="customerCheckoutUserId"/>
        <result property="sdPlannedCompletionDate" column="sdPlannedCompletionDate"/>
        <result property="sdCompletionDate" column="sdCompletionDate"/>
        <result property="prPlannedCompletionDate" column="prPlannedCompletionDate"/>
        <result property="prCompletionDate" column="prCompletionDate"/>
        <result property="testPlannedCompletionDate" column="testPlannedCompletionDate"/>
        <result property="testCompletionDate" column="testCompletionDate"/>
        <result property="initialTotalEstimatedCost" column="initialTotalEstimatedCost"/>
        <result property="sdEstimatedCost" column="sdEstimatedCost"/>
        <result property="sdCost" column="sdCost"/>
        <result property="prEstimatedCost" column="prEstimatedCost"/>
        <result property="prCost" column="prCost"/>
        <result property="testEstimatedCost" column="testEstimatedCost"/>
        <result property="testCost" column="testCost"/>
        <result property="teamCheckpassedDate" column="teamCheckpassedDate"/>
        <result property="customerCheckpassedDate" column="customerCheckpassedDate"/>
        <result property="requNo" column="requNo"/>
        <result property="requSeq" column="requSeq"/>
        <result property="devDispatchDate" column="devDispatchDate"/>
        <result property="acceptanceCost" column="acceptanceCost"/>
        <result property="acceptanceDay" column="acceptanceDay"/>
        <result property="reworkNum" column="reworkNum"/>
    </resultMap>
    <resultMap id="issuekbsharemap" type="com.digiwin.escloud.issueservice.model.IssueKbshare">
        <result property="issueId" column="issueId"/>
        <result property="crmId" column="crmId"/>
        <result property="productCode" column="productCode"/>
        <result property="shareContent" column="shareContent"/>
        <result property="kbid" column="kbid"/>
        <result property="shareUrl" column="shareUrl"/>
        <result property="finishSearchChatFile" column="finishSearchChatFile"/>
        <result property="chatFileContent" column="chatFileContent"/>
    </resultMap>

    <resultMap id="chatFileConfigmap" type="com.digiwin.escloud.issueservice.model.ChatFileConfig">
        <result property="id" column="id"/>
        <result property="productCode" column="productCode"/>
        <result property="authorization" column="authorization"/>
        <result property="tenantsid" column="tenantsid"/>
        <result property="one_directory" column="one_directory"/>
        <result property="two_directory" column="two_directory"/>
        <result property="three_directory" column="three_directory"/>
    </resultMap>

    <select id="selectUndownedIssueList" resultMap="issueselectmap">
        SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,a.ProductVersion as productVersion,
        cd.IssueId as detail_issueId,cd.issueClassification as detail_issueClassification,cd.ErpSystemCode as detail_erpSystemCode,
        cd.ProgramCode as detail_programCode,cd.ProgramVersion as detail_programVersion,cd.SyncStatus as detail_syncStatus,
        ip.Id as progress_id,ip.IssueId as progress_issueId,ip.CrmId as progress_crmId,
        ip.SequenceNum as progress_sequeceNum,ip.ProcessType as progress_processType,ip.workno as progress_processor,
        /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
        CASE WHEN a.ProductCode IN ('100','06','999','164','147') THEN fnStripTags(ip.Description) ELSE ip.Description END as progress_description,
        date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,ip.ReplyType as progress_replyType,
        ip.ProcessHours as progress_processHours,
        a.UserContactId as userContactId,
        /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
        CASE WHEN a.ProductCode IN ('100','06','999','164','147') THEN fnStripTags(a.IssueDescription) ELSE a.IssueDescription END as issueDescription,
        a.IssueStack as issueStack,a.SubmitWay,a.SubmitTime as submitTime,
        IF(a.ProductCode in ('100','06','164') , (IF(a.serviceId= ifnull(a.UserId,''),h.workno,ifnull(b.workno, h.workno))) , ifnull(b.workno, e.ServiceStaffCode)) as serviceStaff ,
        IF(a.ProductCode in ('100','06','164'),css.departmentcode,s.departmentcode) as department,
        ifnull(g.notename, f.`Name`) as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
        f.phone02 as userContact_phone02,f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_userId,
        IF(c.CustomerName IS NULL OR TRIM(c.CustomerName) = '', d.AE002, c.CustomerName) as customerName,
        a.IssueStatus as issueStatus, a.issueCloseInform as issueCloseInform, a.site,a.question_title
        FROM (SELECT * FROM issue WHERE SyncStatus IN('N','E') AND IssueStatus != 'I'
        <if test="inProductCodes != null ">
            <foreach collection="inProductCodes" item="item" open=" AND issue.ProductCode  IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <if test="excludeProductCodes != null ">
            <foreach collection="excludeProductCodes" item="item" open=" AND issue.ProductCode NOT IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        LIMIT 0, #{end}) a
        LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
        LEFT JOIN issue_casedetail cd on a.IssueId = cd.IssueId and (cd.SyncStatus != 'Z' OR ifnull(cd.IssueClassification,'')<![CDATA[<>]]> '')
        LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
        LEFT JOIN user_contacts f on f.Id = a.UserContactId
        LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode
        LEFT JOIN serae as d on e.CustomerServiceCode = d.AE001
        LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)
        LEFT JOIN issue_progress ip on ip.SyncStatus != 'Z' and ip.ProcessType IN('Close','25','26','6') and a.IssueId = ip.IssueId
        LEFT JOIN mars_userpersonalinfo g on f.UserId = g.userid
        LEFT JOIN mars_userpersonalinfo h on a.main_charge= h.userid
        LEFT JOIN mars_customerservicesatff css on css.workno = ifnull(b.workno, h.workno)
    </select>
    <select id="selectUndownedIssueListForTW" resultMap="issueselectmap">
        SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,a.ProductVersion as productVersion,
        cd.IssueId as detail_issueId,cd.issueClassification as detail_issueClassification,cd.ErpSystemCode as detail_erpSystemCode,
        cd.ProgramCode as detail_programCode,cd.ProgramVersion as detail_programVersion,cd.SyncStatus as detail_syncStatus,cd.chatfileHelp as detail_chatfileHelp,
        ip.Id as progress_id,ip.IssueId as progress_issueId,ip.CrmId as progress_crmId,
        ip.SequenceNum as progress_sequeceNum,ip.ProcessType as progress_processType,ip.workno as progress_processor,
        /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
        CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(ip.Description) ELSE ip.Description END as progress_description,
        date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,ip.ReplyType as progress_replyType,
        ip.ProcessHours as progress_processHours,
        a.UserContactId as userContactId,
        /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
        CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(a.IssueDescription) ELSE a.IssueDescription END as issueDescription,
        a.IssueStack as issueStack,a.SubmitWay,a.SubmitTime as submitTime,
        ifnull(b.workno, e.ServiceStaffCode) as serviceStaff, s.departmentcode as department,
        f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
        f.phone02 as userContact_phone02,f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_userId,
        IF(c.CustomerName IS NULL OR TRIM(c.CustomerName) = '', d.AE002, c.CustomerName) as customerName,
        a.IssueStatus as issueStatus, a.issueCloseInform as issueCloseInform, a.site,
        ik.Kbid as kbshare_kbid,ik.ShareContent as kbshare_shareContent,
        ik.FinishSearchChatFile as kbshare_finishSearchChatFile, ik.ChatFileContent as kbshare_chatFileContent
        FROM (SELECT * FROM issue WHERE (SyncStatus='N' or SyncStatus='T') AND IssueStatus != 'I'
            <if test="whiteListInfo != null and whiteListInfo.size > 0">
                AND ServiceCode NOT IN
                <foreach collection="whiteListInfo" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            LIMIT 0, #{end}) a
        LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
        LEFT JOIN issue_casedetail cd on a.IssueId = cd.IssueId and cd.SyncStatus = 'T'
        LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
        LEFT JOIN user_contacts f on f.Id = a.UserContactId
        LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode
        LEFT JOIN serae as d on e.CustomerServiceCode = d.AE001
        LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)
        LEFT JOIN issue_progress ip on ip.SyncStatus = 'T' and a.IssueId = ip.IssueId
        LEFT JOIN issue_kbshare ik on  a.IssueId = ik.IssueId
    </select>
    <select id="selectUndownedIssueListForTW_new" resultMap="issueselectmap">
        SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,a.ProductVersion as productVersion,
               cd.IssueId as detail_issueId,cd.issueClassification as detail_issueClassification,cd.ErpSystemCode as detail_erpSystemCode,
               cd.ProgramCode as detail_programCode,cd.ProgramVersion as detail_programVersion,cd.SyncStatus as detail_syncStatus,cd.chatfileHelp as detail_chatfileHelp,
               ip.Id as progress_id,ip.IssueId as progress_issueId,ip.CrmId as progress_crmId,
               ip.SequenceNum as progress_sequeceNum,ip.ProcessType as progress_processType,ip.workno as progress_processor,
               ip.isOpenToSubmit as progress_isOpenToSubmit ,ip.crm_BR002 as progress_crm_BR002,ip.crm_BR003 as progress_crm_BR003,
            /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
               CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(ip.Description) ELSE ip.Description END as progress_description,
               date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,ip.ReplyType as progress_replyType,
               ip.ProcessHours as progress_processHours,
               a.UserContactId as userContactId,
            /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
               CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(a.IssueDescription) ELSE a.IssueDescription END as issueDescription,
               a.IssueStack as issueStack,a.SubmitWay,a.SubmitTime as submitTime,
               ifnull(b.workno, e.ServiceStaffCode) as serviceStaff, s.departmentcode as department,
               f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
               f.phone02 as userContact_phone02,f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_userId,
               IF(c.CustomerName IS NULL OR TRIM(c.CustomerName) = '', d.AE002, c.CustomerName) as customerName,
               a.IssueStatus as issueStatus, a.issueCloseInform as issueCloseInform, a.site,
                ik.Kbid as kbshare_kbid,ik.ShareContent as kbshare_shareContent,
                ik.FinishSearchChatFile as kbshare_finishSearchChatFile, ik.ChatFileContent as kbshare_chatFileContent

                FROM (SELECT * FROM issue WHERE (SyncStatus='N' or SyncStatus='T'  ) AND IssueStatus != 'I'
                   <if test="allServiceCode == false and whiteListInfo != null and whiteListInfo.size > 0">
                       AND ServiceCode IN
                       <foreach collection="whiteListInfo" index="index" item="item" open="(" separator="," close=")">
                           #{item}
                       </foreach>
                   </if>
                    LIMIT 0, #{end}) a
                 LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
                 LEFT JOIN issue_casedetail cd on a.IssueId = cd.IssueId and cd.SyncStatus = 'T'
                 LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
                 LEFT JOIN user_contacts f on f.Id = a.UserContactId
                 LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode
                 LEFT JOIN serae as d on e.CustomerServiceCode = d.AE001
                 LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)
                 LEFT JOIN issue_progress ip on ip.SyncStatus = 'T' and a.IssueId = ip.IssueId
                 LEFT JOIN issue_kbshare ik on  a.IssueId = ik.IssueId
    </select>
    <select id="selectUndownedIssueProgressListForTW_new" resultMap="issueselectmap">
        SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,a.ProductVersion as productVersion, a.SyncStatus as syncStatus,
        cd.IssueId as detail_issueId,cd.issueClassification as detail_issueClassification,cd.ErpSystemCode as detail_erpSystemCode,
        cd.ProgramCode as detail_programCode,cd.ProgramVersion as detail_programVersion,cd.SyncStatus as detail_syncStatus,cd.chatfileHelp as detail_chatfileHelp,
        ip.Id as progress_id,ip.IssueId as progress_issueId,ip.CrmId as progress_crmId, ip.SyncStatus as progress_syncStatus,
        ip.SequenceNum as progress_sequeceNum,ip.ProcessType as progress_processType,ip.workno as progress_processor,
        ip.isOpenToSubmit as progress_isOpenToSubmit ,ip.crm_BR002 as progress_crm_BR002,ip.crm_BR003 as progress_crm_BR003,
        /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
        CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(ip.Description) ELSE ip.Description END as progress_description,
        date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,ip.ReplyType as progress_replyType,
        ip.ProcessHours as progress_processHours,
        a.UserContactId as userContactId,
        /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
        CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(a.IssueDescription) ELSE a.IssueDescription END as issueDescription,
        a.IssueStack as issueStack,a.SubmitWay,a.SubmitTime as submitTime,
        ifnull(b.workno, e.ServiceStaffCode) as serviceStaff, s.departmentcode as department,
        f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
        f.phone02 as userContact_phone02,f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_userId,
        IF(c.CustomerName IS NULL OR TRIM(c.CustomerName) = '', d.AE002, c.CustomerName) as customerName,
        a.IssueStatus as issueStatus, a.issueCloseInform as issueCloseInform, a.site,
       ik.Kbid as kbshare_kbid,ik.ShareContent as kbshare_shareContent,
       ik.FinishSearchChatFile as kbshare_finishSearchChatFile, ik.ChatFileContent as kbshare_chatFileContent

        FROM (SELECT * From
            (select issue.* from issue_progress join issue on  issue_progress.issueId =  issue.issueId AND issue.IssueStatus != 'I'  where issue_progress.SyncStatus ='P'
             UNION
             select issue.* from issue where issue.SyncStatus ='H' AND issue.IssueStatus != 'I') tmpa
        LIMIT 0, #{end}) a
        LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
        LEFT JOIN issue_casedetail cd on a.IssueId = cd.IssueId
        LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
        LEFT JOIN user_contacts f on f.Id = a.UserContactId
        LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode
        LEFT JOIN serae as d on e.CustomerServiceCode = d.AE001
        LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)
        LEFT JOIN issue_progress ip on ip.SyncStatus = 'P' and a.IssueId = ip.IssueId
        LEFT JOIN issue_kbshare ik on  a.IssueId = ik.IssueId
    </select>
    <select id="getPluginWhiteListinfo" resultType="java.lang.String">
        SELECT whitelistInfo FROM plugin_whitelist
        WHERE psId= #{pluginId}
    </select>
    <select id="selectSyncFailIssueListForTW" resultMap="issueselectmap">
        SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,a.ProductVersion as productVersion,
               cd.IssueId as detail_issueId,cd.issueClassification as detail_issueClassification,cd.ErpSystemCode as detail_erpSystemCode,
               cd.ProgramCode as detail_programCode,cd.ProgramVersion as detail_programVersion,cd.SyncStatus as detail_syncStatus,cd.chatfileHelp as detail_chatfileHelp,
               ip.Id as progress_id,ip.IssueId as progress_issueId,ip.CrmId as progress_crmId,
               ip.SequenceNum as progress_sequeceNum,ip.ProcessType as progress_processType,ip.workno as progress_processor,
            /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
               CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(ip.Description) ELSE ip.Description END as progress_description,
               date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,ip.ReplyType as progress_replyType,
               ip.ProcessHours as progress_processHours,
               a.UserContactId as userContactId,
            /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
               CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(a.IssueDescription) ELSE a.IssueDescription END as issueDescription,
               a.IssueStack as issueStack,a.SubmitWay,a.SubmitTime as submitTime,
               ifnull(b.workno, e.ServiceStaffCode) as serviceStaff, s.departmentcode as department,
               f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
               f.phone02 as userContact_phone02,f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_userId,
               IF(c.CustomerName IS NULL OR TRIM(c.CustomerName) = '', d.AE002, c.CustomerName) as customerName,
               a.IssueStatus as issueStatus, a.issueCloseInform as issueCloseInform, a.site,
                ik.Kbid as kbshare_kbid,ik.ShareContent as kbshare_shareContent,
                ik.FinishSearchChatFile as kbshare_finishSearchChatFile, ik.ChatFileContent as kbshare_chatFileContent
               FROM (SELECT * FROM issue WHERE SyncStatus='E' AND IssueStatus != 'I'
                   <if test="whiteListInfo != null and whiteListInfo.size > 0">
                        AND ServiceCode NOT IN
                        <foreach collection="whiteListInfo" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                   </if>
                   LIMIT 0, #{end}) a
                 LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
                 LEFT JOIN issue_casedetail cd on a.IssueId = cd.IssueId and cd.SyncStatus = 'T'
                 LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
                 LEFT JOIN user_contacts f on f.Id = a.UserContactId
                 LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode
                 LEFT JOIN serae as d on e.CustomerServiceCode = d.AE001
                 LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)
                 LEFT JOIN issue_progress ip on ip.SyncStatus = 'T' and a.IssueId = ip.IssueId
                 LEFT JOIN issue_kbshare ik on  a.IssueId = ik.IssueId
    </select>
    <select id="selectSyncFailIssueListForTW_new" resultMap="issueselectmap">
        SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,a.ProductVersion as productVersion,
               cd.IssueId as detail_issueId,cd.issueClassification as detail_issueClassification,cd.ErpSystemCode as detail_erpSystemCode,
               cd.ProgramCode as detail_programCode,cd.ProgramVersion as detail_programVersion,cd.SyncStatus as detail_syncStatus,cd.chatfileHelp as detail_chatfileHelp,
               ip.Id as progress_id,ip.IssueId as progress_issueId,ip.CrmId as progress_crmId,
               ip.SequenceNum as progress_sequeceNum,ip.ProcessType as progress_processType,ip.workno as progress_processor,
               ip.isOpenToSubmit as progress_isOpenToSubmit ,ip.crm_BR002 as progress_crm_BR002,ip.crm_BR003 as progress_crm_BR003,
            /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
               CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(ip.Description) ELSE ip.Description END as progress_description,
               date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,ip.ReplyType as progress_replyType,
               ip.ProcessHours as progress_processHours,
               a.UserContactId as userContactId,
            /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
               CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(a.IssueDescription) ELSE a.IssueDescription END as issueDescription,
               a.IssueStack as issueStack,a.SubmitWay,a.SubmitTime as submitTime,
               ifnull(b.workno, e.ServiceStaffCode) as serviceStaff, s.departmentcode as department,
               f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
               f.phone02 as userContact_phone02,f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_userId,
               IF(c.CustomerName IS NULL OR TRIM(c.CustomerName) = '', d.AE002, c.CustomerName) as customerName,
               a.IssueStatus as issueStatus, a.issueCloseInform as issueCloseInform, a.site,
               ik.Kbid as kbshare_kbid,ik.ShareContent as kbshare_shareContent,
               ik.FinishSearchChatFile as kbshare_finishSearchChatFile, ik.ChatFileContent as kbshare_chatFileContent
               FROM (SELECT * FROM issue WHERE SyncStatus='E' AND IssueStatus != 'I'

                    <if test="allServiceCode == false and whiteListInfo != null and whiteListInfo.size > 0">
                        AND ServiceCode IN
                        <foreach collection="whiteListInfo" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    LIMIT 0, #{end}) a
                 LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
                 LEFT JOIN issue_casedetail cd on a.IssueId = cd.IssueId and cd.SyncStatus = 'T'
                 LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
                 LEFT JOIN user_contacts f on f.Id = a.UserContactId
                 LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode
                 LEFT JOIN serae as d on e.CustomerServiceCode = d.AE001
                 LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)
                 LEFT JOIN issue_progress ip on ip.SyncStatus = 'T' and a.IssueId = ip.IssueId
                 LEFT JOIN issue_kbshare ik on  a.IssueId = ik.IssueId
    </select>
    <update id="updateIssueCRMID">
        UPDATE issue SET CrmId = #{crmId} , SyncStatus = 'Y', IssueStatus = #{issueStatus}
        <if test="attachmentUploadStatus != null and attachmentUploadStatus != ''">
            , AttachmentUploadStatus = #{attachmentUploadStatus}
        </if>
	    WHERE IssueId = #{issueId}
    </update>
    <update id="updateIssueSyncStatus">
        UPDATE issue SET SyncStatus =  #{syncStatus}
        WHERE IssueId = #{issueId}
    </update>
    <update id="updateIssueTWCRMID">
        UPDATE issue SET CrmId = #{crmId} , SyncStatus = #{syncStatus}, IssueStatus = #{issueStatus}
        <if test="attachmentUploadStatus != null and attachmentUploadStatus != ''">
            , AttachmentUploadStatus = #{attachmentUploadStatus}
        </if>
        WHERE IssueId = #{issueId}
    </update>
    <update id="updateIssueBodySyncStatusTWCRMID">
        UPDATE issue_progress SET  SyncStatus = #{syncStatus}
        <if test="crm_BR002 != null and crm_BR002 != ''">
            , crm_BR002 = #{crm_BR002}
        </if>
        <if test="crm_BR003 != null and crm_BR003 != ''">
            , crm_BR003 = #{crm_BR003}
        </if>
        WHERE IssueId = #{issueId} and Id = #{progressId}
    </update>
    <insert id="insertIssueProgress">
        INSERT INTO issue_progress (IssueId,CrmId,SequenceNum,ProcessType,Processor,Description,ProcessTime)
        VALUES (#{IssueId}, #{CrmId}, #{SequenceNum}, #{ProcessType}, #{Processor},#{Description}, #{ProcessTime})
    </insert>

    <insert id="insertIssueProgressNew" useGeneratedKeys="true" parameterType="com.digiwin.escloud.issueservice.model.IssueProgress"
            keyProperty="id" keyColumn="Id">
        INSERT INTO issue_progress (IssueId,CrmId,SequenceNum,ProcessType,Processor,Description,ProcessTime,ProcessHours, ReplyType)
        VALUES (#{issueId}, #{crmId}, #{sequeceNum}, #{processType}, #{processor},#{description}, #{processTime}, #{processHours}, #{replyType})
    </insert>

    <select id="isExistIssueProgress" resultType="java.lang.Object">
        SELECT ifnull(IssueId , -1) as IssueId
        FROM issue_progress
        WHERE IssueId = #{IssueId} and CrmId = #{CrmId} and SequenceNum = #{SequenceNum} and ProcessTime LIKE #{ProcessTime}
    </select>

    <select id="isExistIssueProgressNew" resultType="java.lang.Object">
        SELECT ifnull(Id , -1) as Id
        FROM issue_progress
        WHERE IssueId = #{IssueId} and CrmId = #{CrmId} and SequenceNum = #{SequenceNum} and ProcessTime LIKE #{ProcessTime}
        LIMIT 0, 1
    </select>

    <update id="updateIssueProgress">
        UPDATE issue_progress SET Processor = #{Processor},Description = #{Description},ProcessHours = #{ProcessHours}, ReplyType =#{ReplyType}
        WHERE IssueId = #{IssueId} and CrmId = #{CrmId} and SequenceNum = #{SequenceNum} and ProcessTime LIKE #{ProcessTime}
    </update>

    <update id="updateIssueStatus">
        UPDATE issue SET IssueStatus = #{issueStatus}
        WHERE IssueId = #{issueId}
    </update>
    <update id="updateIssueStatusbycrmid">
        UPDATE issue SET IssueStatus = #{issueStatus},
        <if test="serviceCode != ''">
            ServiceCode=#{serviceCode},
        </if>
        <if test="productCode != ''">
            ProductCode=#{productCode},
        </if>
        <if test="userId != ''">
            UserId=#{userId},
        </if>
        <if test="userContactId != ''">
            UserContactId=#{userContactId},
        </if>
        ProductVersion = #{programVersion}, IssueDescription = #{issueDescription}, ServiceDepartment =
        #{serviceDepartment}, SupportDepartment = #{supportDepartment},
        ServiceId = (SELECT b.userid FROM mars_customerservicesatff a LEFT JOIN mars_staffaccount b ON a.itcode =
        b.itcode WHERE workno = #{serviceStaff} LIMIT 1),
        SupportId = (SELECT b.userid FROM mars_customerservicesatff a LEFT JOIN mars_staffaccount b ON a.itcode =
        b.itcode WHERE workno = #{supportStaff} LIMIT 1)
        WHERE CrmId = #{crmId}
    </update>
    <insert id="InsertOrUpdateIssueCaseDetailByIssueId">
        INSERT issue_casedetail (IssueId, IssueClassification, ErpSystemCode, ProgramCode, SyncStatus
        <if test="agreeDate !=null and agreeDate  != '' ">
            ,expected_completion_date
        </if>
        <if test="agreeDateSubmiter !=null and agreeDateSubmiter  != '' ">
            ,agreeDateSubmiter
        </if>
        <if test="agreeDateReply !=null and agreeDateReply != ''">
            ,agreeDateReply
        </if>
        <if test="sendReplyToClient != null and  sendReplyToClient != '' ">
            ,hasNewAgreeDateReply
        </if>
        <if test="chatfileHelp !=null and chatfileHelp != ''">
            ,chatfileHelp
        </if>
        <if test="totalWorkHours != null  ">
            ,total_work_hours
        </if>
            )
        VALUES (#{issueId}, #{issueClassification}, #{erpSystemCode}, #{programCode}, 'T'
        <if test="agreeDate !=null and agreeDate  != '' ">
           ,#{agreeDate}
        </if>
        <if test="agreeDateSubmiter !=null and agreeDateSubmiter  != '' ">
            ,#{agreeDateSubmiter}
        </if>
        <if test="agreeDateReply !=null and agreeDateReply != ''">
            ,#{agreeDateReply}
        </if>
        <if test="sendReplyToClient != null and  sendReplyToClient != ''  ">
            <if test='sendReplyToClient =="N" '>
                ,false
            </if>
            <if test='sendReplyToClient =="Y" '>
                , true
            </if>
        </if>
        <if test="chatfileHelp !=null and chatfileHelp != ''">
            ,#{chatfileHelp}
        </if>
        <if test="totalWorkHours != null  ">
            ,#{totalWorkHours}
        </if>
        )
        ON DUPLICATE KEY UPDATE IssueClassification = VALUES(IssueClassification), ErpSystemCode = VALUES(ErpSystemCode),
        ProgramCode = VALUES(ProgramCode)
        <if test="agreeDate !=null and agreeDate  != '' ">
            ,expected_completion_date = #{agreeDate}
        </if>
        <if test="agreeDateSubmiter !=null and agreeDateSubmiter  != '' ">
            ,agreeDateSubmiter= #{agreeDateSubmiter}
        </if>
        <if test="agreeDateReply !=null and agreeDateReply != ''">
            ,agreeDateReply=#{agreeDateReply}
        </if>
        <if test="sendReplyToClient != null and  sendReplyToClient != ''">
            <if test='sendReplyToClient =="N" '>
                , hasNewAgreeDateReply=false
            </if>
            <if test='sendReplyToClient =="Y" '>
                ,hasNewAgreeDateReply= true
            </if>
        </if>
        <if test="chatfileHelp !=null and chatfileHelp != ''">
            ,chatfileHelp=#{chatfileHelp}
        </if>
        <if test="totalWorkHours != null  ">
            ,total_work_hours=#{totalWorkHours}
        </if>
    </insert>
    <update id="updateIssueDetailbyissueId">
      insert into  issue_casedetail(IssueId, ProgramCode, ProgramVersion, SyncStatus)
      values(#{issueId},#{programCode},#{programVersion},'Z')
        ON DUPLICATE KEY UPDATE ProgramCode = #{programCode}, ProgramVersion = #{programVersion}
    </update>

    <insert id="insertIssueProgresstw">
        INSERT INTO issue_progress (IssueId,CrmId,SequenceNum,ProcessType,Processor,Description,ProcessTime,ProcessHours, ReplyType,crm_BR002,crm_BR003,innerDes)
        select IssueId, #{CrmId}, #{SequenceNum}, #{ProcessType}, #{Processor},#{Description}, #{ProcessTime}, ifnull(#{processHours},0), #{replyType}, #{BR002}, #{BR003},#{innerDes}
        from issue issue
        where issue.CrmId = #{CrmId}
    </insert>
    <!--VALUES (#{IssueId}, #{CrmId}, #{SequenceNum}, #{ProcessType}, #{Processor},#{Description}, #{ProcessTime})-->

    <insert id="insertIssueProgressCN" useGeneratedKeys="true" keyProperty="id" keyColumn="Id">
        <selectKey resultType="long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID() as id
        </selectKey>
        INSERT INTO issue_progress (IssueId,CrmId,SequenceNum,ProcessType,Processor,Description,ProcessTime,ProcessHours, ReplyType,
        SyncStatus, workno, handlerId, CurrentStatus)
        select IssueId, #{CrmId}, #{SequenceNum}, #{ProcessType}, #{Processor},#{Description}, #{ProcessTime}, ifnull(#{processHours},0),
        #{replyType}, #{syncStatus}, #{workno}, #{handlerId},#{CurrentStatus}
        from issue issue
        where issue.CrmId = #{CrmId}
    </insert>

    <select id="isExistIssueProgresstw" resultType="java.lang.String">
        SELECT ifnull(CrmId , -1) as CrmId
        FROM issue_progress
        WHERE CrmId = #{CrmId} and SequenceNum = #{SequenceNum}   and ProcessTime LIKE #{ProcessTime}
        limit 0,1
    </select>
    <select id="isExistIssueProgresstw_new" resultType="java.lang.String">
        SELECT Id as Id
        FROM issue_progress
        WHERE CrmId = #{CrmId}
          and ((CrmId = #{CrmId} and crm_BR002=#{BR002} and crm_BR003=#{BR003})
           or (CrmId = #{CrmId} and SequenceNum =#{SequenceNum}  and ProcessTime  LIKE #{ProcessTime} and ProcessType not in('Submit') and crm_BR002 is null ))
        order by crm_BR002 desc
        limit 0,1
    </select>
    <update id="updateIssueProgresstw">
        UPDATE issue_progress SET Processor = #{Processor},Description = #{Description},ProcessHours = #{ProcessHours}, ReplyType =#{ReplyType}
        WHERE CrmId = #{CrmId} and SequenceNum = #{SequenceNum} and ProcessTime LIKE #{ProcessTime} and ProcessType='Process'
    </update>
    <update id="updateIssueProgresstw_new">
        UPDATE issue_progress SET Processor = #{Processor},Description = #{Description},ProcessHours = #{ProcessHours}, ReplyType =#{ReplyType},crm_BR002=#{BR002},crm_BR003=#{BR003},innerDes =#{innerDes}
        WHERE Id = #{Id}
    </update>
    <delete id="deleteIssueProgresstw_new">
        DELETE FROM issue_progress
        WHERE Id = #{Id}
    </delete>
    <select id="getLastIssueProgress" resultType="com.digiwin.escloud.issueservice.model.IssueProgress">
        SELECT b.Id id , b.IssueId issueId,b.CrmId , b.ReplyType replyType,b.ProcessType processType
        FROM ISsue_progress b
        WHERE b.ISsueId  IN (SELECT a.issueId FROM ISsue_progress a WHERE a.Id = #{id}) AND b.Id != #{id}
        ORDER BY b.Id DESC
        LIMIT 1
    </select>
    <delete id="deleteIssueProgresstw">
        DELETE FROM issue_progress
        WHERE CrmId = #{CrmId} and SequenceNum = #{SequenceNum} and ProcessTime LIKE #{ProcessTime}
    </delete>

    <select id="SelectUnSyncIssues"  resultMap="issueselectmap">
        SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as
        productCode,a.ProductVersion as productVersion,
        a.UserContactId as userContactId,CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN
        fnStripTags(a.IssueDescription) ELSE a.IssueDescription END as issueDescription,
        a.IssueStack as issueStack,a.SubmitWay, date_format(a.SubmitTime, '%Y-%m-%d %H:%i:%s') as
        submitTime,a.IssueStatus as issueStatus,
        IF(a.ProductCode in ('100','06','164') , (IF(a.serviceId= ifnull(a.UserId,''),h.workno,ifnull(b.workno, h.workno))) , ifnull(b.workno, e.ServiceStaffCode)) as serviceStaff ,
        IF(a.ProductCode in ('100','06','164'),css.departmentcode,s.departmentcode) as department, a.SyncStatus as
        syncStatus ,
        ifnull(g.notename, f.`Name`) as userContact_name, f.email as userContact_email, f.phone01 as
        userContact_phone01,
        f.phone02 as userContact_phone02,f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_userId,
        c.CustomerName as customerName,
        cd.IssueId as detail_issueId,cd.issueClassification as detail_issueClassification,cd.ErpSystemCode as
        detail_erpSystemCode,
        cd.ProgramCode as detail_programCode,cd.ProgramVersion as detail_programVersion,cd.SyncStatus as
        detail_syncStatus,
        ip.Id as progress_id,ip.IssueId as progress_issueId,ip.CrmId as progress_crmId,
        ip.SequenceNum as progress_sequeceNum,ip.ProcessType as progress_processType,ip.Processor as
        progress_processor,
        CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN if(TRIM(fnStripTags(ifnull(ip.Description,''))) != '',TRIM(fnStripTags(ifnull(ip.Description,''))),TRIM(fnStripTags(sm.message))) ELSE ip.Description
        END as progress_description,
        date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,ip.ReplyType as progress_replyType,
        ip.ProcessHours as progress_processHours,ip.SyncStatus as progress_syncStatus,
        IF(a.ProductCode in ('100','06','164') , ifnull(ip.workno, h.workno) , ip.workno) as progress_workno,
        IF(ip.ProcessType in('Close','25','26','6'), ifnull(ip.workno, ifnull(h.workno,b.workno)),'') as progress_closeServiceStaff ,
        IF(ip.ProcessType in('Close','25','26','6'),cs.departmentcode,'') as progress_closeDepartment,ip.CurrentStatus as
        progress_currentStatus,a.question_title as question_title,ip.crm_BR002 as progress_crm_BR002,ip.crm_BR003 as progress_crm_BR003
        from (
            SELECT a.*
            FROM issue a
            where a.SyncStatus = 'T'
            <if test="inProductCodes != null ">
                <foreach collection="inProductCodes" item="item" open=" AND a.ProductCode  IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="excludeProductCodes != null ">
                <foreach collection="excludeProductCodes" item="item" open=" AND a.ProductCode NOT IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            UNION
            SELECT a.*
            FROM issue a
            INNER JOIN issue_casedetail cd on cd.IssueId = a.IssueId and cd.SyncStatus = 'T' and
            ifnull(cd.IssueClassification,'')<![CDATA[<>]]> ''
            WHERE 1=1 and a.syncStatus NOT IN('Z','N','E')
            <if test="inProductCodes != null ">
                <foreach collection="inProductCodes" item="item" open=" AND a.ProductCode  IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="excludeProductCodes != null ">
                <foreach collection="excludeProductCodes" item="item" open=" AND a.ProductCode NOT IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            UNION
            SELECT a.*
            FROM issue a
            INNER JOIN issue_progress ip on ip.IssueId = a.IssueId and ip.SyncStatus = 'T'
            AND ip.ProcessType in ('1','5','6','9','13','14','15','16','17','18','19','20','21','23','24','25','26','42','43','Close','Process')
            LEFT JOIN service_message sm on  ip.IssueId=sm.issueId AND sm.sequenceNum=ip.SequenceNum
            WHERE 1=1 and a.syncStatus NOT IN('Z','N','E')
            and (TRIM(fnStripTags(ifnull(ip.Description,'')))!='' or TRIM(fnStripTags(ifnull(sm.message,''))) !='' )
            <if test="inProductCodes != null ">
                <foreach collection="inProductCodes" item="item" open=" AND a.ProductCode  IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="excludeProductCodes != null ">
                <foreach collection="excludeProductCodes" item="item" open=" AND a.ProductCode NOT IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            ORDER BY SubmitTime desc
            LIMIT 0,20
        ) AS a
        LEFT JOIN issue_casedetail cd on cd.IssueId = a.IssueId and (cd.SyncStatus = 'T' OR ifnull(cd.IssueClassification,'') <![CDATA[<>]]> '' )
        LEFT JOIN issue_progress ip on ip.IssueId = a.IssueId and
                                       ((ip.SyncStatus = 'T'
                                            AND ip.ProcessType in ('1','5','6','9','13','14','15','16','17','18','19','20','21','23','24','25','26','42','43','Close','Process'))
                                       OR ip.ProcessType IN('Close','25','26','6'))
        LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
        LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
        LEFT JOIN user_contacts f on f.Id = a.UserContactId
        LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode
        LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)
        LEFT JOIN mars_userpersonalinfo g on f.UserId = g.userid
        LEFT JOIN mars_userpersonalinfo h on a.main_charge= h.userid
        LEFT JOIN mars_customerservicesatff cs on cs.workno = ifnull(ip.workno, h.workno)
        LEFT JOIN mars_customerservicesatff css on css.workno = ifnull(b.workno, h.workno)
        LEFT JOIN service_message sm on  ip.IssueId=sm.issueId AND sm.sequenceNum=ip.SequenceNum
    </select>
    <!--SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,a.ProductVersion as productVersion,-->
    <!--a.UserContactId as userContactId,a.IssueDescription as issueDescription,a.IssueStack as issueStack,a.SubmitWay, date_format(a.SubmitTime, '%Y-%m-%d %H:%i:%s') as submitTime,a.IssueStatus as issueStatus,-->
    <!--ifnull(b.workno, e.ServiceStaffCode) as serviceStaff , s.departmentcode as department, a.SyncStatus as syncStatus ,-->
    <!--f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,-->
    <!--f.phone02 as userContact_phone02, f.qq as userContact_qq, a.UserContactId as userContact_userId,-->
    <!--c.CustomerName as customerName,-->
    <!--cd.IssueId as detail_issueId,cd.issueClassification as detail_issueClassification,cd.ErpSystemCode as detail_erpSystemCode,-->
    <!--cd.ProgramCode as detail_programCode,cd.ProgramVersion as detail_programVersion,cd.SyncStatus as detail_syncStatus,-->

    <!--ip.Id as progress_id,ip.IssueId as progress_issueId,ip.CrmId as progress_crmId,-->
    <!--ip.SequenceNum as progress_sequenceNum,ip.ProcessType as progress_processType,ip.Processor as progress_processor,-->
    <!--ip.Description as progress_description,date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,ip.ReplyType as progress_replyType,-->
    <!--ip.ProcessHours as progress_processHours-->

    <!--FROM issue a-->
    <!--LEFT JOIN issue_casedetail cd on cd.IssueId = a.IssueId and cd.SyncStatus = 'T'-->
    <!--LEFT JOIN issue_progress ip on ip.IssueId = a.IssueId and ip.SyncStatus = 'T'-->
    <!--LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId-->
    <!--LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)-->
    <!--LEFT JOIN user_contacts f on f.Id = a.UserContactId-->
    <!--LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode-->
    <!--LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)-->
    <!--WHERE  'T' in ( a.SyncStatus, cd.SyncStatus, ip.SyncStatus)-->
    <!--LIMIT 0, 20-->
    <select id="getIssueSyncStatus" resultType="java.lang.String">
        SELECT SyncStatus FROM issue
        where IssueId = #{issueId}
    </select>
    <select id="getIssueDetailSyncStatus" resultType="java.lang.String">
        SELECT SyncStatus FROM issue_casedetail
        where IssueId = #{issueId}
    </select>
    <select id="getIssueProgressSyncStatus" resultType="java.lang.String">
        SELECT SyncStatus FROM issue_progress
        where Id = #{Id}
    </select>

    <update id="updateIssueSync">
        UPDATE issue SET SyncStatus = 'Y'
        WHERE IssueId = #{issueId}
    </update>

    <update id="updateIssueDetailSync">
        UPDATE issue_casedetail SET SyncStatus = 'Y'
        WHERE IssueId = #{id}
    </update>

    <update id="updateIssueProgressSync" parameterType="com.digiwin.escloud.issueservice.model.IssueProgressSyncResult">
        UPDATE issue_progress SET SyncStatus = 'Y', CrmId = #{crmid}, SequenceNum = #{sequenceNum}
        <if test="BR002 != null and BR002 != ''">
            , crm_BR002 = #{BR002}
        </if>
        <if test="BR003 != null and BR003 != ''">
            , crm_BR003 = #{BR003}
        </if>
        WHERE Id = #{id}
    </update>

    <update id="updateIssueSyncV2">
        UPDATE issue SET SyncStatus = 'X'
        WHERE IssueId = #{issueId} and SyncStatus != 'N'
    </update>

    <update id="updateIssueDetailSyncV2">
        UPDATE issue_casedetail SET SyncStatus = 'X'
        WHERE IssueId = #{issueId}
    </update>

    <update id="updateIssueProgressSyncV2">
        UPDATE issue_progress SET SyncStatus = 'X'
        WHERE Id = #{id}
    </update>

    <update id="UpdateIssueSyncForX">
        UPDATE issue SET SyncStatus = 'T'
        WHERE IssueId = #{issueId} and SyncStatus ='X'
    </update>

    <update id="UpdateIssueDetailSyncForX">
        UPDATE issue_casedetail SET SyncStatus = 'T'
        WHERE IssueId = #{issueId} and SyncStatus ='X'
    </update>

    <update id="UpdateIssueProgressSyncForX">
        UPDATE issue_progress SET SyncStatus = 'T'
        WHERE IssueId = #{issueId} AND ProcessType in ('1','5','6','9','13','14','15','16','17','18','19','20','21','23','24','25','26','42','43','Close','Process') and SyncStatus ='X'
    </update>

    <select id="SelectIssueSync" resultType="java.lang.String">
        SELECT IssueId
        FROM issue_sync
        WHERE SyncTimes in (1,2,3) AND SyncStatus='E'
    </select>

    <update id="UpdateIssueSyncStatus">
        UPDATE issue_sync SET SyncStatus = 'Y'
        WHERE IssueId = #{issueId}
    </update>

    <insert id="InsertIssueSync">
        insert into issue_sync(IssueId,CrmId,IssueStatus,SyncStatus,ErrorMsg,SyncTimes,SyncTime)
        values(#{IssueId},#{CrmId},#{IssueStatus},#{SyncStatus},#{ErrorMsg},#{SyncTimes},#{SyncTime})
        ON DUPLICATE KEY UPDATE IssueStatus=#{IssueStatus},SyncStatus=#{SyncStatus},ErrorMsg=#{ErrorMsg}
        ,SyncTimes=SyncTimes+1,SyncTime=#{SyncTime}
    </insert>

    <insert id="insertIssue" useGeneratedKeys="true" parameterType="com.digiwin.escloud.issueservice.model.Issue"
            keyProperty="issueId" keyColumn="IssueId">
        INSERT INTO issue (CrmId, ServiceCode,ProductCode,ProductVersion,UserId,
        IssueDescription,IssueStack,SubmitWay,IssueStatus,SubmitTime,
        <if test="userContactId !=null and userContactId  != '' ">
        UserContactId,
        </if>
         ServiceId, ServiceDepartment, SupportId, SupportDepartment, IssueType, SyncStatus,
        planDownloadTime, planUpdateTime)
        VALUES (#{crmId}, #{serviceCode}, #{productCode}, #{productVersion}, #{userId},
        #{issueDescription}, #{issueStack}, #{submitWay}, #{issueStatus}, #{submitTime},
        <if test="userContactId !=null and userContactId  != '' ">
        #{userContactId},
        </if>
        #{serviceStaff}, #{department}, #{supportStaff}, #{supportDepartment},'P', 'Z',
        #{planDownloadTime}, #{planUpdateTime})
    </insert>

    <insert id="insertIssueCN" useGeneratedKeys="true" parameterType="com.digiwin.escloud.issueservice.model.Issue"
            keyProperty="issueId" keyColumn="IssueId">
        INSERT INTO issue (CrmId, ServiceCode,ProductCode,ProductVersion,UserId,
        IssueDescription,IssueStack,SubmitWay,IssueStatus,SubmitTime,
        UserContactId, ServiceId, ServiceDepartment, IssueType, SyncStatus,
        planDownloadTime, planUpdateTime, main_charge)
        VALUES (#{crmId}, #{serviceCode}, #{productCode}, #{productVersion}, #{userId},
        #{issueDescription}, #{issueStack}, #{submitWay}, #{issueStatus}, #{submitTime},
        #{userContactId}, #{serviceStaff}, #{department}, 'P', #{syncStatus},
        #{planDownloadTime}, #{planUpdateTime}, #{serviceStaff})
    </insert>

    <select id="IsExitsIssue" resultType="java.lang.Object">
        SELECT ifnull(IssueId, -1)
        from issue
        where CrmId = #{crmid}
        limit 0, 1
    </select>

    <update id="updateIssue" parameterType="com.digiwin.escloud.issueservice.model.Issue">
        UPDATE issue SET ServiceCode=#{serviceCode}, ProductCode=#{productCode}, ProductVersion=#{productVersion},
        IssueDescription=#{issueDescription}, IssueStatus=#{issueStatus}, UserId=#{userId},
        <if test="userContactId !=null and userContactId  != '' ">
        UserContactId=#{userContactId},
        </if>
        ServiceId=#{serviceStaff}, ServiceDepartment=#{department},
        SupportId=#{supportStaff}, SupportDepartment=#{supportDepartment}, SubmitTime = #{submitTime}
        WHERE IssueId = #{issueId}
    </update>

    <update id="updateIssueCN" parameterType="com.digiwin.escloud.issueservice.model.Issue">
        UPDATE issue SET ServiceCode=#{serviceCode}, ProductCode=#{productCode}, ProductVersion=#{productVersion},
        IssueDescription=#{issueDescription}, IssueStatus=#{issueStatus}, UserId=#{userId},
        UserContactId=#{userContactId}, ServiceId=#{serviceStaff}, ServiceDepartment=#{department},
        SubmitTime = #{submitTime}, SyncStatus = #{syncStatus}
        WHERE IssueId = #{issueId}
    </update>

    <delete id="deleteIssue" parameterType="com.digiwin.escloud.issueservice.model.Issue">
        DELETE issue
        WHERE IssueId = #{issueId}
    </delete>

    <insert id="InsertIssueCaseDetail">
        insert into issue_casedetail(IssueId,IssueClassification,ErpSystemCode,ProgramCode,SyncStatus
        <if test="expectedCompletionDate !=null and expectedCompletionDate  != '' ">
            ,expected_completion_date
        </if>
        <if test="agreeDateSubmiter !=null and agreeDateSubmiter  != '' ">
            ,agreeDateSubmiter
        </if>
        <if test="agreeDateReply !=null and agreeDateReply != ''">
            ,agreeDateReply
        </if>
        <if test="hasNewAgreeDateReply != null and  hasNewAgreeDateReply != '' ">
            ,hasNewAgreeDateReply
        </if>
        <if test="chatfileHelp != null and  chatfileHelp != '' ">
            ,chatfileHelp <if test="totalWorkHours != null  ">
            ,total_work_hours
        </if>
        </if>

)
        values(#{issueId},#{issueClassification},#{erpSystemCode},#{programCode},'Z'
        <if test="expectedCompletionDate !=null and expectedCompletionDate  != '' ">
            ,#{expectedCompletionDate}
        </if>
        <if test="agreeDateSubmiter !=null and agreeDateSubmiter  != '' ">
            ,#{agreeDateSubmiter}
        </if>
        <if test="agreeDateReply !=null and agreeDateReply != ''">
            ,#{agreeDateReply}
        </if>
        <if test="hasNewAgreeDateReply != null and  hasNewAgreeDateReply != ''  ">
            ,#{hasNewAgreeDateReply}
        </if>
        <if test="chatfileHelp != null and  chatfileHelp != '' ">
            ,#{chatfileHelp}
        </if>
        <if test="totalWorkHours != null  ">
            ,#{totalWorkHours}
        </if>
        )
        ON DUPLICATE KEY UPDATE IssueClassification=#{issueClassification},ErpSystemCode=#{erpSystemCode},
        ProgramCode=#{programCode}
        <if test="expectedCompletionDate !=null and expectedCompletionDate  != '' ">
            ,expected_completion_date = #{expectedCompletionDate}
        </if>
        <if test="agreeDateSubmiter !=null and agreeDateSubmiter  != '' ">
            ,agreeDateSubmiter= #{agreeDateSubmiter}
        </if>
        <if test="agreeDateReply !=null and agreeDateReply != ''">
            ,agreeDateReply=#{agreeDateReply}
        </if>
        <if test="hasNewAgreeDateReply != null and  hasNewAgreeDateReply != ''">
            , hasNewAgreeDateReply=#{hasNewAgreeDateReply}
        </if>
        <if test="chatfileHelp != null and  chatfileHelp != ''">
            , chatfileHelp=#{chatfileHelp}
        </if>
        <if test="totalWorkHours != null  ">
            ,total_work_hours=#{totalWorkHours}
        </if>
    </insert>

    <insert id="InsertIssueSummaryCloseTime">
        insert into issue_summary(IssueId,ClosedTime )
        values(#{issueId},#{closeTime}  )
        ON DUPLICATE KEY UPDATE ClosedTime=#{closeTime}
    </insert>

    <insert id="InsertIssueCaseDetailCN">
        insert into issue_casedetail(IssueId,IssueClassification,ErpSystemCode,ProgramCode,SyncStatus,total_work_hours)
        values(#{issueId},#{issueClassification},#{erpSystemCode},#{programCode},#{syncStatus},#{totalWorkHours})
        ON DUPLICATE KEY UPDATE IssueClassification=#{issueClassification},ErpSystemCode=#{erpSystemCode},
        ProgramCode=#{programCode},total_work_hours=#{totalWorkHours}
    </insert>

    <select id="selectNewRegisteredUser" resultType="java.lang.String">
        SELECT a.email
        FROM mars_user a
        LEFT OUTER JOIN mars_newuserissuesynced b ON a.email = b.email
        WHERE a.UserType = '1' AND b.email IS NULL
    </select>

    <select id="selectIssue" resultType="java.lang.String">
        SELECT a.email
        FROM mars_user a
        LEFT OUTER JOIN mars_newuserissuesynced b ON a.email = b.email
        WHERE a.UserType = '1' AND b.email IS NULL
    </select>

    <insert id="batchInsertIssue" useGeneratedKeys="true" keyProperty="issueId" keyColumn="IssueId">
        INSERT IGNORE INTO issue (CrmId, ServiceCode, ProductCode, ProductVersion, UserId,
        IssueDescription, IssueStack, SubmitWay, IssueStatus, SubmitTime,
        UserContactId, ServiceId, ServiceDepartment, IssueType, SyncStatus,
        planDownloadTime, planUpdateTime)
        VALUES
        <foreach item="item" collection="list" separator=",">
            (#{item.crmId}, #{item.serviceCode}, #{item.productCode}, #{item.productVersion}, #{item.userId},
            #{item.issueDescription}, #{item.issueStack}, #{item.submitWay}, #{item.issueStatus}, #{item.submitTime},
            #{item.userContactId}, #{item.serviceStaff}, #{item.department}, 'P', 'Z',
            #{item.planDownloadTime}, #{item.planUpdateTime})
        </foreach>
    </insert>

    <insert id="batchInsertIssueCaseDetail">
        INSERT IGNORE INTO issue_casedetail(IssueId, IssueClassification, ErpSystemCode, ProgramCode, SyncStatus)
        VALUES
        <foreach item="item" collection="list" separator=",">
            (#{item.issueId}, #{item.issueClassification}, #{item.erpSystemCode}, #{item.programCode}, 'Z')
        </foreach>
    </insert>

    <insert id="batchInsertIssueProgresstw">
        INSERT IGNORE INTO issue_progress (IssueId, CrmId, SequenceNum, ProcessType, Processor, Description, ProcessTime,
        ProcessHours, ReplyType)
        VALUES
        <foreach item="item" collection="list" separator=",">
            (#{item.issueId}, #{item.crmId}, #{item.sequenceNum}, #{item.processType}, #{item.processor},
            #{item.description}, #{item.processTime}, #{item.processHours}, #{item.replyType})
        </foreach>
    </insert>

    <insert id="batchInsertIssueProgressCN">
        INSERT INTO issue_progress (IssueId, CrmId, SequenceNum, ProcessType, Processor, Description, ProcessTime,
        ProcessHours, ReplyType, SyncStatus)
        VALUES
        <foreach item="item" collection="list" separator=",">
            (#{item.issueId}, #{item.crmId}, #{item.sequenceNum}, #{item.processType}, #{item.processor},
            #{item.description}, #{item.processTime}, #{item.processHours}, #{item.replyType}, #{item.syncStatus})
        </foreach>
    </insert>

    <insert id="batchInsertNewUserIssueSynced">
        INSERT IGNORE INTO mars_newuserissuesynced (Email, InsertTime)
        VALUES
        <foreach item="item" collection="list" separator=",">
            (#{item.email}, #{item.insertTime})
        </foreach>
    </insert>

    <select id="getIssueServiceID" resultType="java.lang.String">
        SELECT ServiceId
        FROM issue
        WHERE IssueId = #{issueId}
    </select>

    <update id="updateIssueServiceIDandDepartment">
        UPDATE issue SET ServiceId=#{ServiceId}, ServiceDepartment=#{ServiceDepartment}
        WHERE IssueId = #{IssueId}
    </update>
    <select id="SelectUnSyncCallCenterIssueList" resultMap="issueselectmap">
        SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,a.ProductVersion as productVersion,
               IF(ip.Id IS NULL , t.progressId, ip.Id) as progress_id,
               ip.IssueId as progress_issueId,ip.CrmId as progress_crmId,
               ip.SequenceNum as progress_sequeceNum,ip.ProcessType as progress_processType,ip.workno as progress_processor,
               ip.Description  as progress_description,
               date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,ip.ReplyType as progress_replyType,
               ip.ProcessHours as progress_processHours,
               a.UserContactId as userContactId,a.IssueDescription  as issueDescription,
               a.IssueStack as issueStack,a.SubmitWay,a.SubmitTime as submitTime,
               ifnull(b.workno, e.ServiceStaffCode) as serviceStaff, s.departmentcode as department,
               f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
               f.phone02 as userContact_phone02,f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_userId,
               IF(c.CustomerName IS NULL OR TRIM(c.CustomerName) = '', d.AE002, c.CustomerName) as customerName,
               a.IssueStatus as issueStatus, a.issueCloseInform as issueCloseInform, a.site
        FROM (SELECT * FROM issue_contactlist WHERE SyncStatus='C' LIMIT 0, 10) t
                 LEFT JOIN issue a on t.issueId=a.issueId
                 LEFT JOIN issue_progress ip on a.IssueId = ip.IssueId and t.progressId=ip.Id and ip.ProcessType ='Process'
                 LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
                 LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
                 LEFT JOIN user_contacts f on f.Id = a.UserContactId
                 LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode
                 LEFT JOIN serae as d on e.CustomerServiceCode = d.AE001
                 LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)
    </select>
    <select id="SelectUnSyncCallCenterIssueProgressList" resultMap="issueselectmap">
        SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,a.ProductVersion as productVersion,
               ip.Id as progress_id,ip.IssueId as progress_issueId,ip.CrmId as progress_crmId,
               ip.SequenceNum as progress_sequeceNum,ip.ProcessType as progress_processType,ip.workno as progress_processor,
               ip.Description  as progress_description,
               date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,ip.ReplyType as progress_replyType,
               ip.ProcessHours as progress_processHours,
               a.UserContactId as userContactId,a.IssueDescription  as issueDescription,
               a.IssueStack as issueStack,a.SubmitWay,a.SubmitTime as submitTime,
               ifnull(b.workno, e.ServiceStaffCode) as serviceStaff, s.departmentcode as department,
               f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
               f.phone02 as userContact_phone02,f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_userId,
               IF(c.CustomerName IS NULL OR TRIM(c.CustomerName) = '', d.AE002, c.CustomerName) as customerName,
               a.IssueStatus as issueStatus, a.issueCloseInform as issueCloseInform, a.site
        FROM (SELECT * FROM issue_contactlist WHERE SyncStatus in('N','E')) t
            INNER JOIN issue_progress ip on ip.IssueId = t.IssueId  and t.ProgressId=ip.Id  AND ip.ProcessType = 'Process' and ip.ReplyType='Q'
            LEFT JOIN issue a on t.IssueId=a.IssueId
            LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
            LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
            LEFT JOIN user_contacts f on f.Id = a.UserContactId
            LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode
            LEFT JOIN serae as d on e.CustomerServiceCode = d.AE001
            LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)
    </select>

    <select id="getIssueSyncToCrmLastTime" resultType="java.lang.String">
        SELECT lastSyncTime
        FROM issue_sync_to_crm
        WHERE syncItem = #{syncItem}
    </select>

    <select id="selectUnDownloadCustomizedBugIssueList" resultMap="issueselectmap">
        SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,a.ProductVersion as productVersion,
        cd.IssueId as detail_issueId,cd.issueClassification as detail_issueClassification,cd.ErpSystemCode as detail_erpSystemCode,
        cd.ProgramCode as detail_programCode,cd.ProgramVersion as detail_programVersion,cd.SyncStatus as detail_syncStatus, cd.requNo as detail_requNo, cd.requSeq as detail_requSeq,
        ip.Id as progress_id,ip.IssueId as progress_issueId,ip.CrmId as progress_crmId,
        ip.SequenceNum as progress_sequeceNum,ip.ProcessType as progress_processType,ip.workno as progress_processor,
        /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
        CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(ip.Description) ELSE ip.Description END as progress_description,
        date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,ip.ReplyType as progress_replyType,
        ip.ProcessHours as progress_processHours,
        a.UserContactId as userContactId,
        /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
        CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(a.IssueDescription) ELSE a.IssueDescription END as issueDescription,
        a.IssueStack as issueStack,a.SubmitWay,a.SubmitTime as submitTime,
        ifnull(b.workno, e.ServiceStaffCode) as serviceStaff, s.departmentcode as department,
        ifnull(g.notename, f.`Name`) as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
        f.phone02 as userContact_phone02,f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_userId,
        IF(c.CustomerName IS NULL OR TRIM(c.CustomerName) = '', d.AE002, c.CustomerName) as customerName,
        a.IssueStatus as issueStatus, a.issueCloseInform as issueCloseInform, a.site,
        e.window_department, mainCharge_s.workno as mainCharge_workNo,
        mainCharge_s.departmentcode as mainCharge_DepartmentCode, ifnull(issue_progress_s.workno,issue_progress_g.`name`) as progress_processorName,
		c.CustomerCode as customerCode, a.question_title, a.emergency, ifnull(issue_g.workno,issue_g.`name`) as userId_name, ifnull(issue_g.departmentCode,'') as userId_departmentcode,ip.CurrentStatus as progress_currentStatus
        FROM (SELECT * FROM issue WHERE (ProductCode in ('100','06','999','164','147')) and __version__> #{lastIssueSyncTime} order by  __version__ LIMIT 0, #{end}) a
        LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
        LEFT JOIN issue_casedetail cd on a.IssueId = cd.IssueId
        LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
        LEFT JOIN user_contacts f on f.Id = a.UserContactId
        LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode
        LEFT JOIN serae as d on e.CustomerServiceCode = d.AE001
        LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)
        LEFT JOIN issue_progress ip on a.IssueId = ip.IssueId
        LEFT JOIN mars_userpersonalinfo g on f.UserId = g.userid
        LEFT JOIN mars_userpersonalinfo mainCharge_g on (a.main_charge = mainCharge_g.userid)
        LEFT JOIN mars_customerservicesatff mainCharge_s on mainCharge_g.workno = mainCharge_s.workno
		LEFT JOIN mars_userpersonalinfo issue_progress_g on ip.Processor = issue_progress_g.userid
        LEFT JOIN mars_customerservicesatff issue_progress_s on  issue_progress_s.workno = issue_progress_g.workno
		LEFT JOIN mars_userpersonalinfo issue_g on a.userId = issue_g.userid
    </select>

    <update id="updateCustomizedBugIssues">
        UPDATE issue_sync_to_crm SET lastSyncTime =(
        SELECT max(issue.__version__) from issue where 1=1 and (
        <foreach item="item" collection="list" separator=" or ">
            IssueId = #{item.issueId}
        </foreach>
        )
        )
        WHERE issue_sync_to_crm.syncItem = 'IssueToCrmCustomizedBug'
    </update>

    <select id="selectUnDownloadCustomizedIssueList" resultMap="issueselectmap">
      SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,a.ProductVersion as productVersion,
        cd.IssueId as detail_issueId,cd.issueClassification as detail_issueClassification,cd.ErpSystemCode as detail_erpSystemCode,
        cd.ProgramCode as detail_programCode,cd.ProgramVersion as detail_programVersion,cd.SyncStatus as detail_syncStatus, cd.requNo as detail_requNo, cd.requSeq as detail_requSeq,
        ip.Id as progress_id,ip.IssueId as progress_issueId,ip.CrmId as progress_crmId,
        ip.SequenceNum as progress_sequeceNum,ip.ProcessType as progress_processType,ip.workno as progress_processor,
        /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
        CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(ip.Description) ELSE ip.Description END as progress_description,
        date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,ip.ReplyType as progress_replyType,
        ip.ProcessHours as progress_processHours,
        a.UserContactId as userContactId,
        /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
        CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(a.IssueDescription) ELSE a.IssueDescription END as issueDescription,
        a.IssueStack as issueStack,a.SubmitWay,a.SubmitTime as submitTime,
        ifnull(b.workno, e.ServiceStaffCode) as serviceStaff, s.departmentcode as department,
        ifnull(g.notename, f.`Name`) as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
        f.phone02 as userContact_phone02,f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_userId,
        IF(c.CustomerName IS NULL OR TRIM(c.CustomerName) = '', d.AE002, c.CustomerName) as customerName,
        a.IssueStatus as issueStatus, a.issueCloseInform as issueCloseInform, a.site,
        e.window_department, mainCharge_s.workno as mainCharge_workNo,
        mainCharge_s.departmentcode as mainCharge_DepartmentCode, ifnull(issue_progress_s.workno,issue_progress_g.`name`) as progress_processorName,
		c.CustomerCode as customerCode, a.question_title, a.emergency, ifnull(issue_g.workno,issue_g.`name`) as userId_name, ifnull(issue_s.departmentCode,'') as userId_departmentcode,
		ifnull(issue_g.email,'') as userId_mail, cd.total_work_hours as detail_totalWorkHours, cd.expected_completion_date as detail_expectedCompletionDate, cd.estimated_work_hours as detail_estimatedWorkHours,
		cd.requExpectCompletionDate as detail_requExpectCompletionDate, cd.requPlanCompletionDate as detail_requPlanCompletionDate, cd.requActualCompletionDate as detail_requActualCompletionDate,ip.CurrentStatus as progress_currentStatus
        FROM issue_to_guwencrm
        LEFT JOIN issue a on a.IssueId = issue_to_guwencrm.IssueId
        LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
        LEFT JOIN issue_casedetail cd on a.IssueId = cd.IssueId
        LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
        LEFT JOIN user_contacts f on f.Id = a.UserContactId
        LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode
        LEFT JOIN serae as d on e.CustomerServiceCode = d.AE001
        LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)
        LEFT JOIN issue_progress ip on a.IssueId = ip.IssueId
        LEFT JOIN mars_userpersonalinfo g on f.UserId = g.userid
        LEFT JOIN mars_userpersonalinfo mainCharge_g on (a.main_charge = mainCharge_g.userid)
        LEFT JOIN mars_customerservicesatff mainCharge_s on mainCharge_g.workno = mainCharge_s.workno
		LEFT JOIN mars_userpersonalinfo issue_progress_g on ip.Processor = issue_progress_g.userid
        LEFT JOIN mars_customerservicesatff issue_progress_s on issue_progress_s.workno = issue_progress_g.workno
		LEFT JOIN mars_userpersonalinfo issue_g on a.userId = issue_g.userid
		LEFT JOIN mars_customerservicesatff issue_s on issue_s.workno = issue_g.workno
		where issue_to_guwencrm.status !='Y'
    </select>

    <update id="updateCustomizedIssuesStatus">
        UPDATE issue_to_guwencrm SET status = 'Y'
        WHERE status != 'Y' and issueId = #{issueId}
    </update>

    <select id="selectUnDownloadAcceptancepassCustomizedIssuesList" resultMap="customizedIssueselectmap">
        SELECT cr.requId as requId,cr.crmId as crmId,cr.customerServiceCode as customerServiceCode,cr.productCode as productCode,cr.isFromCustomer as isFromCustomer,cr.erpSystemCode as erpSystemCode,
        cr.programCode as programCode,cr.type as `type`,cr.difficultyLevel as difficultyLevel,cr.priority as priority,cr.title as title,cr.description as description,cr.submitWay as submitWay,cr.`status` as `status`,
        cr.submitTime as submitTime,cr.modifyTime as modifyTime,cr.userId as userId,cr.submitter as submitter,cr.handlerId as handlerId,cr.plannedCompletionDate as plannedCompletionDate,cr.completionDate as completionDate,
        crs.isCheckPassWithoutIssue as isCheckPassWithoutIssue,crs.isAcceptWithoutIssue as isAcceptWithoutIssue,crs.isSdSpec as isSdSpec,crs.isTestSpec as isTestSpec,crs.sdUserId as sdUserId,crs.prUserId as prUserId,
        crs.testUserId as testUserId,crs.teamCheckoutUserId as teamCheckoutUserId,crs.customerCheckoutUserId as customerCheckoutUserId,crs.sdPlannedCompletionDate as sdPlannedCompletionDate,crs.sdCompletionDate as sdCompletionDate,
        crs.prPlannedCompletionDate as prPlannedCompletionDate,crs.prCompletionDate as prCompletionDate,crs.testPlannedCompletionDate as testPlannedCompletionDate,crs.testCompletionDate as testCompletionDate,crs.initialTotalEstimatedCost as initialTotalEstimatedCost,
        crs.sdEstimatedCost as sdEstimatedCost,crs.sdCost as sdCost,crs.prEstimatedCost as prEstimatedCost,crs.prCost as prCost,crs.testEstimatedCost as testEstimatedCost,crs.testCost as testCost,crs.teamCheckpassedDate as teamCheckpassedDate,
        crs.customerCheckpassedDate as customerCheckpassedDate,crs.requNo as requNo,crs.requSeq as requSeq,
        ifnull(userId_g.workno,userId_g.`name`) as userId_name, ifnull(userId_s.departmentCode,'') as userId_departmentcode,
        ifnull(sdUserId_g.workno,sdUserId_g.`name`) as sdUserId_name, ifnull(sdUserId_s.departmentCode,'') as sdUserId_departmentcode,
        ifnull(prUserId_g.workno,prUserId_g.`name`) as prUserId_name, ifnull(prUserId_s.departmentCode,'') as prUserId_departmentcode,
        ifnull(testUserId_g.workno,testUserId_g.`name`) as testUserId_name, ifnull(testUserId_s.departmentCode,'') as testUserId_departmentcode,
        ifnull(teamCheckoutUserId_g.workno,teamCheckoutUserId_g.`name`) as teamCheckoutUserId_name, ifnull(teamCheckoutUserId_s.departmentCode,'') as teamCheckoutUserId_departmentcode,
        crp.processTime as devDispatchDate,crship.crmId as issueCrmId,min(dispath_crp.processTime) as sdDispatchDate,
        acceptanceCost,
        CASE WHEN cr.`status` IN ('D_A','D_F') AND testCompletionDate is not null AND teamCheckpassedDate is not null
          THEN TIMESTAMPDIFF(day,testCompletionDate,teamCheckpassedDate)
          WHEN  cr.`status` IN ('D_C') AND testCompletionDate is not null
          THEN TIMESTAMPDIFF(day,testCompletionDate,NOW())
          ELSE '' END acceptanceDay,
        ifnull(refuseNum,0)  + IFNULL(bugNum,0) reworkNum
        FROM (SELECT * FROM customized_requ WHERE (customized_requ.`status` = 'D_C' OR customized_requ.`status` = 'D_K' OR customized_requ.`status` = 'D_F') and customized_requ.modifyTime> #{lastSyncTime} order by customized_requ.modifyTime ) cr
        INNER JOIN customized_requ_s crs ON cr.requId = crs.requId  and crs.requNo is not null and crs.requSeq is not null
         LEFT JOIN mars_userpersonalinfo userId_g on cr.userId = userId_g.userid
        LEFT JOIN mars_customerservicesatff userId_s on userId_s.workno = userId_g.workno
        LEFT JOIN mars_userpersonalinfo sdUserId_g on crs.sdUserId = sdUserId_g.userid
        LEFT JOIN mars_customerservicesatff sdUserId_s on sdUserId_s.workno = sdUserId_g.workno
        LEFT JOIN mars_userpersonalinfo prUserId_g on crs.prUserId = prUserId_g.userid
        LEFT JOIN mars_customerservicesatff prUserId_s on prUserId_s.workno = prUserId_g.workno
        LEFT JOIN mars_userpersonalinfo testUserId_g on crs.testUserId = testUserId_g.userid
        LEFT JOIN mars_customerservicesatff testUserId_s on testUserId_s.workno = testUserId_g.workno
        LEFT JOIN mars_userpersonalinfo teamCheckoutUserId_g on crs.teamCheckoutUserId = teamCheckoutUserId_g.userid
        LEFT JOIN mars_customerservicesatff teamCheckoutUserId_s on teamCheckoutUserId_s.workno = teamCheckoutUserId_g.workno
        LEFT JOIN customized_requ_progress crp  on cr.requId = crp.requId and  crp.processType='P2_DEVELOPER'
        LEFT JOIN crmid_relationship crship on crship.subCrmId = cr.crmId
        LEFT JOIN customized_requ_progress dispath_crp  on cr.requId = dispath_crp.requId
        LEFT JOIN (SELECT requId,COUNT(*)  refuseNum  from customized_requ_progress
            WHERE  processType IN ('P_BACK2_DEVELOPER','P_BACK2_TESTER')
                GROUP BY requId) ta1 ON ta1.requId = cr.requId
        LEFT JOIN (SELECT requId,SUM(cost) acceptanceCost from customized_requ_progress
                WHERE `status` ='D_C'
                GROUP BY requId
                )ta4 ON ta4.requId = cr.requId
        LEFT JOIN (SELECT crs.CrmId, COUNT(*)  bugNum   FROM crmid_relationship crs
                WHERE crs.subCrmId IN (
                SELECT a.crmId FROM issue a
                join issue_casedetail b on a.IssueId = b.IssueId
                WHERE b.IssueClassification = '8'
                )
                GROUP BY crs.CrmId )ta3 ON ta3.CrmId = cr.CrmId
        group by cr.requId
        LIMIT 0, #{end}
    </select>

    <select id="selectRelatedCustomizedIssuesAcceptancepass" resultType="java.lang.Integer">
        select count(cr.`status`) as issueCount from crmid_relationship crs
        join (SELECT crmId FROM crmid_relationship where subCrmId = #{crmId}) a on a.crmId = crs.crmId
        join customized_requ cr on cr.crmId = crs.subCrmId  and (cr.`status` !='D_C' and cr.`status` !='D_K' and cr.`status` !='D_F')
    </select>

    <update id="updateIssueSyncToCrmTime">
        UPDATE issue_sync_to_crm SET lastSyncTime = #{lastIssueTime}
        WHERE issue_sync_to_crm.syncItem = #{syncItem}
    </update>

    <select id="checkCustomizedIssue" resultType="java.lang.String">
        SELECT cs.requId from  customized_requ_s crs
        LEFT JOIN customized_requ cs on cs.requId=crs.requId
        WHERE crs.requNo = #{requNo} and  requSeq = #{requSeq}
        LIMIT 0,1
    </select>

    <select id="getUserIdByWorknoOrItcode" resultType="java.lang.String">
       SELECT mu.userId from mars_customerservicesatff mcs
       LEFT JOIN mars_userpersonalinfo mu on  mu.workno =mcs.workno
       WHERE mcs.workno= #{userName}  or mu.name= #{userName}
       LIMIT 0,1
    </select>

    <select id="getServiceCodeByCustomerCode" resultType="java.lang.String">
        SELECT mc.CustomerServiceCode from mars_customer mc
        WHERE mc.CustomerCode= #{customerCode}
        LIMIT 0,1
    </select>

    <select id="getMaxReuqId" resultType="java.lang.Integer">
        select ifnull(max(requId),0)  from  customized_requ
    </select>

    <update id="updateCustomizedIssue">
        UPDATE customized_requ SET productCode = #{productCode},erpSystemCode = #{erpSystemCode},programCode = #{programCode}, title =#{title}, description =#{description}, priority =#{priority}, modifyTime =#{modifyTime}
        WHERE requId = #{requId}
    </update>

    <update id="updateCustomized_sIssue">
        UPDATE customized_requ_s SET sdUserId = #{sdUserId},
        <if test="sdPlannedCompletionDate != '' and sdPlannedCompletionDate !=null">
            sdPlannedCompletionDate = #{sdPlannedCompletionDate},
        </if>
        <if test="sdCompletionDate != '' and sdCompletionDate !=null">
            sdCompletionDate = #{sdCompletionDate},
        </if>
        <if test="sdEstimatedCost != '' and sdEstimatedCost !=null">
            sdEstimatedCost =#{sdEstimatedCost},
        </if>
        <if test="sdCost != '' and sdCost !=null">
            sdCost =#{sdCost},
        </if>
        <if test="initialTotalEstimatedCost != '' and initialTotalEstimatedCost !=null">
            initialTotalEstimatedCost = #{initialTotalEstimatedCost},
        </if>
        <if test="prEstimatedCost != '' and prEstimatedCost !=null">
            prEstimatedCost= #{prEstimatedCost},
        </if>
        modifyTime = #{modifyTime}
        WHERE requId = #{requId}
    </update>

    <insert id="insertCustomizedIssue">
        INSERT INTO customized_requ (crmId,customerServiceCode,productCode,isFromCustomer,erpSystemCode,programCode,priority,title,
        description,submitWay,
        status,
        <if test="submitTime != '' and submitTime !=null">
            submitTime,
        </if>
        <if test="modifyTime != '' and modifyTime !=null">
            modifyTime,
        </if>
        userId,submitter,handlerId)
        VALUES (#{crmId}, #{serviceCode}, #{productCode},0, #{erpSystemCode}, #{programCode},#{priority}, #{title}
        , #{description},'INNER', 'D_S',
        <if test="submitTime != '' and submitTime !=null">
            #{submitTime},
        </if>
        <if test="modifyTime != '' and modifyTime !=null">
            #{modifyTime},
        </if>
        #{userId}, #{userId}, #{sdUserId})
    </insert>

    <insert id="insertCustomized_sIssue">
        INSERT INTO customized_requ_s (requId,isCheckPassWithoutIssue,isAcceptWithoutIssue,sdUserId,
        <if test="sdPlannedCompletionDate != '' and sdPlannedCompletionDate !=null">
          sdPlannedCompletionDate,
        </if>
        <if test="sdCompletionDate != '' and sdCompletionDate !=null">
          sdCompletionDate,
        </if>
        <if test="modifyTime != '' and modifyTime !=null">
            modifyTime,
        </if>
        <if test="sdEstimatedCost != '' and sdEstimatedCost !=null">
            sdEstimatedCost,
        </if>
        <if test="sdCost != '' and sdCost !=null">
            sdCost,
        </if>
        <if test="initialTotalEstimatedCost != '' and initialTotalEstimatedCost !=null">
            initialTotalEstimatedCost,
        </if>
        <if test="prEstimatedCost != '' and prEstimatedCost !=null">
            prEstimatedCost,
        </if>
        requNo,requSeq   )
        VALUES (#{requId}, #{isCheckPassWithoutIssue}, #{isAcceptWithoutIssue}, #{sdUserId},
        <if test="sdPlannedCompletionDate != '' and sdPlannedCompletionDate !=null">
          #{sdPlannedCompletionDate},
        </if>
        <if test="sdCompletionDate != '' and sdCompletionDate !=null">
          #{sdCompletionDate},
        </if>
        <if test="modifyTime != '' and modifyTime !=null">
            #{modifyTime},
        </if>
        <if test="sdEstimatedCost != '' and sdEstimatedCost !=null">
            #{sdEstimatedCost},
        </if>
        <if test="sdCost != '' and sdCost !=null">
            #{sdCost},
        </if>
        <if test="initialTotalEstimatedCost != '' and initialTotalEstimatedCost !=null">
            #{initialTotalEstimatedCost},
        </if>
        <if test="prEstimatedCost != '' and prEstimatedCost !=null">
            #{prEstimatedCost},
        </if>
        #{requNo}, #{requSeq}  )
    </insert>

    <insert id="insertCrmidRelationship">
        INSERT INTO crmid_relationship (crmId,subCrmId )
        VALUES (#{requNo},#{crmId})
    </insert>

    <insert id="insertCustomizedProgress">
        INSERT INTO customized_requ_progress (requId,sequenceNum,processorId,processorWorkno,processType,status,
        <if test="submitTime != '' and submitTime !=null">
            processTime,
        </if>
        handlerId, is_sync187 )
        VALUES (#{requId},'1', #{userId}, #{userName},'P_SUBMIT','D_S',
        <if test="submitTime != '' and submitTime !=null">
            #{submitTime},
        </if>
        #{sdUserId},0)
    </insert>
    <select id="selectCallCenterUnuploadFiles" resultMap="issueselectmap">
        SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,a.ProductVersion as productVersion,
               cd.IssueId as detail_issueId,cd.issueClassification as detail_issueClassification,cd.ErpSystemCode as detail_erpSystemCode,
               cd.ProgramCode as detail_programCode,cd.ProgramVersion as detail_programVersion,cd.SyncStatus as detail_syncStatus,
               t.progressId as progress_id,t.IssueId as progress_issueId,t.CrmId as progress_crmId,
            /*因为T产品线描述可以使用html标签，但是这些标签没有同步到CRM的必要，因此只针对T产品特殊处理，不影响其他产品别*/
               CASE WHEN a.ProductCode in ('100','06','999','164','147') THEN fnStripTags(a.IssueDescription) ELSE a.IssueDescription END as issueDescription,
               a.IssueStack as issueStack,a.SubmitWay,a.SubmitTime as submitTime,
               ifnull(b.workno, e.ServiceStaffCode) as serviceStaff, s.departmentcode as department,
               f.`Name` as userContact_name, f.email as userContact_email, f.phone01 as userContact_phone01,
               f.phone02 as userContact_phone02,f.phone03 as userContact_phone03, f.qq as userContact_qq, a.UserContactId as userContact_userId,
               IF(c.CustomerName IS NULL OR TRIM(c.CustomerName) = '', d.AE002, c.CustomerName) as customerName,
               a.IssueStatus as issueStatus, a.issueCloseInform as issueCloseInform, a.site
        FROM (SELECT * FROM issue_contactlist WHERE SyncAnnex in('N','E') LIMIT 0, #{end}) t
                 LEFT JOIN issue a on t.issueId=a.issueId
                 LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
                 LEFT JOIN issue_casedetail cd on a.IssueId = cd.IssueId and cd.SyncStatus = 'T'
                 LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
                 LEFT JOIN user_contacts f on f.Id = a.UserContactId
                 LEFT JOIN mars_customer c on c.CustomerServiceCode = a.ServiceCode
                 LEFT JOIN serae as d on e.CustomerServiceCode = d.AE001
                 LEFT JOIN mars_customerservicesatff s on s.workno = ifnull(b.workno, e.ServiceStaffCode)
    </select>
    <update id="updateIssueFileUploadStatus">
        UPDATE issue SET AttachmentUploadStatus = #{attachmentUploadStatus}
        WHERE IssueId = #{issueId}
    </update>

    <select id="checkIssueIsEdrEvent" resultType="java.lang.String">
        SELECT sourceId FROM issue_source_map
        where 1=1 and sourceType ='EDREvent'
        <if test="crmId != ''">
            and issueCode = #{crmId}
        </if>
        limit 1
    </select>
    <update id="UpdateIssueAdditionalExplanationSyncAnnex">
        UPDATE issue_contactlist
        SET SyncAnnex=#{syncStatus}
        where IssueId = #{issueId}
        <if test="progressId != ''">
            and ProgressId=#{progressId}
        </if>

    </update>

    <select id="selectUnSearchChatFileIssueForTW" resultMap="issueselectmap">
        SELECT ik.IssueId as issueId, ik.CrmId as crmId ,ik.ProductCode as productCode, issue.ServiceCode as serviceCode
        FROM  issue_kbshare  ik
        inner join issue on ik.CrmId= issue.CrmId
        where 1=1
        and ( ik.FinishSearchChatFile is null or ik.FinishSearchChatFile='F' )
        order by ik.IssueId
        LIMIT 0, #{end}
    </select>

    <select id="selectIssueSearchContent" resultType="java.lang.String">
        SELECT IssueDescription FROM issue
        where 1=1
        <if test="crmId != ''">
            and CrmId = #{crmId}
        </if>
        limit 1
    </select>

    <select id="selectIssueInfoByCrmId" resultMap="issueselectmap">
        SELECT IssueDescription as issueDescription,serviceRegion as serviceRegion,ProductCode as productCode FROM issue
        where 1=1
        <if test="crmId != ''">
            and CrmId = #{crmId}
        </if>
        limit 1
    </select>

    <update id="updateIssueKbShare">
        UPDATE issue_kbshare
        SET
        <if test="chatFileSearchText != ''">
            ChatFileSearchText=#{chatFileSearchText},
        </if>
        <if test="chatFileContent != ''">
            ChatFileContent=#{chatFileContent},
        </if>
        <if test="chatFileError != ''">
            ChatFileErrorInfo=#{chatFileError},
        </if>
        <if test="invalidChatFileAnswer != null">
            InvalidChatFileAnswer=#{invalidChatFileAnswer},
        </if>
        <if test="chatfileKnowledgeList !=null and chatfileKnowledgeList != ''">
            ChatfileKnowledgeList=#{chatfileKnowledgeList},
        </if>
        <if test="invalidChatFileKnowledgeNo != null">
            invalidChatFileKnowledgeNo=#{invalidChatFileKnowledgeNo},
        </if>
        FinishSearchChatFile = #{finishSearchChatFile}
        where CrmId = #{crmId} and (FinishSearchChatFile is null or FinishSearchChatFile !='T')
    </update>

    <update id="updateIssueKbShare_smart">
        UPDATE issue_kbshare
        SET
            KbId=#{kbId},
            SearchText =#{searchText},
            ShareContent=#{shareContent},
            ShareUrl=#{shareUrl}
        where CrmId = #{crmId}
    </update>

    <select id="getCharFileConfig" resultMap="chatFileConfigmap">
        SELECT icc.id as id,icc.productCode as productCode,ifnull(icc.authorization,'') as authorization,ifnull(icc.tenantsid,'') as tenantsid, ifnull(icc.rootDirectory,5) as rootDirectory,
        ifnull(icc.one_directory,'') as one_directory,ifnull(icc.two_directory,'') as two_directory, ifnull(icc.three_directory,'') as three_directory
        FROM  issue_chatfile_config icc
        where 1=1
          and productCode = #{productCode}
            and issueChatFileSearch = true
        <if test="serviceRegion != ''">
            and serviceRegion=#{serviceRegion}
        </if>
        limit 1
    </select>

    <select id="selectUnDownloadedIssuekbshareForTW" resultMap="issueselectmap">
        SELECT a.IssueId as issueId, a.CrmId as crmId ,a.ServiceCode as serviceCode,a.ProductCode as productCode,
               ip.SequenceNum as progress_sequeceNum, date_format(ip.ProcessTime, '%Y-%m-%d %H:%i:%s') as progress_processTime,
               ip.crm_BR002 as progress_crm_BR002,ip.crm_BR003 as progress_crm_BR003,
               a.SubmitWay,a.SubmitTime as submitTime,ifnull(b.workno, e.ServiceStaffCode) as serviceStaff ,
               a.IssueStatus as issueStatus,
               ik.Kbid as kbshare_kbid,ik.ShareContent as kbshare_shareContent,
               ik.FinishSearchChatFile as kbshare_finishSearchChatFile, ik.ChatFileContent as kbshare_chatFileContent,
               ifnull(mu.`language`,"zh-TW") as userLanguage
        FROM  (select issue_kbshare.* from issue_kbshare
               where issue_kbshare.FinishSearchChatFile ='T' and (issue_kbshare.SyncStatus is NULL or issue_kbshare.SyncStatus='N')  ) ik
                  inner JOIN issue a on  a.IssueId = ik.IssueId  and (a.SyncStatus='Y' or a.SyncStatus='T')
                  LEFT JOIN issue_progress ip on ip.IssueId = ik.IssueId and ip.Id=
                  ( select issue_progress.Id from issue_progress where ik.IssueId = issue_progress.IssueId order by issue_progress.SequenceNum desc limit 1)
                  LEFT JOIN mars_userpersonalinfo b on b.userid = a.ServiceId
                  LEFT JOIN mars_customerservice e on e.CustomerServiceCode = a.ServiceCode and (ifnull(a.ProductCode,'') = '' or e.ProductCode = a.ProductCode)
                  LEFT JOIN mars_userpersonalinfo mu on a.UserId=mu.userid
        LIMIT 0,#{end}
    </select>

    <update id="updateTWIssuekbshareSyncStatus">
        UPDATE issue_kbshare SET SyncStatus = #{syncStatus}, SyncTime = #{syncTime}
        WHERE CrmId = #{crmId} and FinishSearchChatFile ='T'  and (SyncStatus is null or SyncStatus='N')
    </update>

    <select id="checkIssueHasNoKbId" resultType="java.lang.String">
        SELECT IFNULL(Kbid,'-1') from issue_kbshare where CrmId=#{CrmId}
            limit 0,1
    </select>

    <select id="getChatfileInvalidAnswerKeyword" resultType="java.lang.String">
        SELECT keyword
        FROM chatfile_noresult_keyword
    </select>

    <insert id="insertChatfileFailLog">
        INSERT INTO chatfile_invalid_log (source,productCode,serviceRegion,searchContent,resultContent,errorInfo,invalidAnswer,invalidChatFileKnowledgeNo)
        VALUES (#{source},#{productCode}, #{serviceRegion}, #{searchContent}, #{resultContent}, #{errorInfo},#{invalidAnswer},#{invalidChatFileKnowledgeNo})
    </insert>

    <select id="SelectIssuesXSync"  resultType="java.lang.String">
        SELECT a.IssueId as issueId
        from (
        SELECT a.*
        FROM issue a
        where a.SyncStatus = 'X'
        <choose>
            <when test='productCodes != null '>
                <foreach collection="productCodes" item="item" open=" AND a.ProductCode  IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <when test='crmIds != null '>
                <foreach collection="crmIds" item="item" open=" AND a.crmId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and  a.`__version__`<![CDATA[<=]]>date_sub(now(),interval 2 hour)
            </otherwise>
        </choose>
        UNION
        SELECT a.*
        FROM issue a
        INNER JOIN issue_casedetail cd on cd.IssueId = a.IssueId and cd.SyncStatus = 'X' and
        ifnull(cd.IssueClassification,'')<![CDATA[<>]]> ''
        WHERE 1=1 and a.syncStatus NOT IN('Z','N','E')
        <choose>
            <when test='productCodes != null '>
                <foreach collection="productCodes" item="item" open=" AND a.ProductCode  IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <when test='crmIds != null '>
                <foreach collection="crmIds" item="item" open=" AND a.crmId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and  cd.`__version__`<![CDATA[<=]]>date_sub(now(),interval 2 hour)
            </otherwise>
        </choose>
        UNION
        SELECT a.*
        FROM issue a
        INNER JOIN issue_progress ip on ip.IssueId = a.IssueId and ip.SyncStatus = 'X'
        AND ip.ProcessType in ('1','5','6','9','13','14','15','16','17','18','19','20','21','23','24','25','26','42','43','Close','Process')
        WHERE 1=1 and a.syncStatus NOT IN('Z','N','E')
        <choose>
            <when test='productCodes != null '>
                <foreach collection="productCodes" item="item" open=" AND a.ProductCode  IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <when test='crmIds != null '>
                <foreach collection="crmIds" item="item" open=" AND a.crmId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and  ip.processTime <![CDATA[<=]]>date_sub(now(),interval 2 hour)
            </otherwise>
        </choose>
        ORDER BY SubmitTime desc
        LIMIT 0,20
        ) AS a
    </select>
</mapper>
