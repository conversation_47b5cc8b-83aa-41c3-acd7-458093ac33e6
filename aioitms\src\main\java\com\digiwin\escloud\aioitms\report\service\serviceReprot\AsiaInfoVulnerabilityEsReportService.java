package com.digiwin.escloud.aioitms.report.service.serviceReprot;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.bigdata.model.AsiaInfoDeviceInfo;
import com.digiwin.escloud.aioitms.bigdata.model.AsiaInfoVuln;
import com.digiwin.escloud.aioitms.es.service.EsService;
import com.digiwin.escloud.aioitms.report.dao.AiopsDeviceTpMapper;
import com.digiwin.escloud.aioitms.report.dao.AiopsThirdPartyReportMapper;
import com.digiwin.escloud.aioitms.report.dao.AsiaVulnerabilityMapper;
import com.digiwin.escloud.aioitms.report.model.*;
import com.digiwin.escloud.aioitms.report.model.enums.AsiaInfoVulnSeverityStatus;
import com.digiwin.escloud.aioitms.report.model.serviceReport.AsiaInfoVulnerabilityEsReport;
import com.digiwin.escloud.aioitms.report.service.AiopsThirdPartyReportService;
import com.digiwin.escloud.aiouser.model.tenant.TenantTpDto;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.JdbcSqlInfo;
import com.digiwin.escloud.common.model.TenantTpParams;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.ConvertUtil;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
@Slf4j
@Service
public class AsiaInfoVulnerabilityEsReportService extends EsService<AsiaInfoVulnerabilityEsReport> implements ParamCheckHelp {
    @Resource
    private AiopsThirdPartyReportMapper aiopsThirdPartyReportMapper;

    @Resource
    private AsiaVulnerabilityMapper asiaVulnerabilityMapper;

    @Resource
    private AiopsDeviceTpMapper aiopsDeviceTpMapper;

    @Autowired
    private AioUserFeignClient aioUserFeignClient;
    @Resource
    private BigDataUtil bigDataUtil;

    @Value("${aio.service.area:CN}")
    private String serviceArea;

    @Resource
    private AiopsThirdPartyReportService aiopsThirdPartyReportService;

    private final static String THIRD_PARTY = "ASIA_INFO";
    private final static String PRODUCT_CODE = "VULN";

    @PostConstruct
    public void init() {
        indexCreate(AsiaInfoVulnerabilityEsReport.class);
    }


    @Transactional(rollbackFor = Exception.class)
    public BaseResponse generateReport(AiopsThirdPartyReport atpr) {
        if (CollectionUtils.isEmpty(atpr.getAsiaDeviceIdList())) {
            return BaseResponse.ok();
        }

        buildAtpr(atpr);
        AsiaInfoVulnerabilityEsReport aivr = buildAivr(atpr);
        aiopsThirdPartyReportMapper.insertAiopsThirdPartyReport(atpr);
        save(aivr, false);
        aiopsThirdPartyReportMapper.updateAiopsThirdPartyReportStatus(ReportStatus.UNDER_EVA.getIndex(), atpr.getId());
        return BaseResponse.ok(aivr.getId());
    }

    public BaseResponse getReport(String id) {
        AsiaInfoVulnerabilityEsReport aivr = findById(id, AsiaInfoVulnerabilityEsReport.class);

        AiopsThirdPartyReport atpr = aiopsThirdPartyReportService.reportRecordGet(LongUtil.objectToLong(id));
        String tenantTpId = atpr.getOtherInfoObject().getTenantTpId();
        List<Map<String, String>> conditionMapList = new ArrayList<>();
        Map<String, String> condition1 = new HashMap<>();
        condition1.put("key", "eid");
        condition1.put("op", "=");
        condition1.put("value", LongUtil.safeToString(atpr.getEid()));

        Map<String, String> condition2 = new HashMap<>();
        condition2.put("key", "tpTenantId");
        condition2.put("op", "=");
        condition2.put("value", tenantTpId);
        conditionMapList.add(condition1);
        conditionMapList.add(condition2);

        Map<String, String> conditionMap = new HashMap<>();
        conditionMap.put("eid", LongUtil.safeToString(atpr.getEid()));
        conditionMap.put("tpTenantId", tenantTpId);

        List<String> asiaDeviceIdList = atpr.getOtherInfoObject().getAsiaDeviceIdList();
        //调用大数据平台查询漏洞数据
        List<AsiaInfoVuln> duplicateAsiaInfoVulnsList = getVulnInfo(atpr.getOtherInfoObject(), conditionMapList, false, asiaDeviceIdList);
        List<AsiaInfoVuln> noDuplicateAsiaInfoVulnsList = getVulnInfo(atpr.getOtherInfoObject(), conditionMapList, true, asiaDeviceIdList);

        //调用大数据平台查询设备数据
        List<AsiaInfoDeviceInfo> asiaInfoDeviceList = getAsiaDevice(asiaDeviceIdList, conditionMap);
        if (CollectionUtils.isEmpty(asiaInfoDeviceList) && CollectionUtils.isEmpty(duplicateAsiaInfoVulnsList)) {
            return BaseResponse.ok(aivr);
        }

        Map<String, AsiaInfoDeviceInfo> deviceInfoMap = asiaInfoDeviceList
                .stream()
                .collect(Collectors.toMap(AsiaInfoDeviceInfo::getDeviceId, Function.identity()));

        //漏洞详情
        List<AsiaInfoVuln> severityVulnList = buildAsiaInfoVulnInfo(noDuplicateAsiaInfoVulnsList, deviceInfoMap,duplicateAsiaInfoVulnsList);
        aivr.setAsiaInfoVulnList(severityVulnList);

        //漏洞特征
        List<AsiaVulnerabilityKB> asiaVulnerabilityKBList = buildAsiaVulnerabilityKBList(noDuplicateAsiaInfoVulnsList);
        aivr.setVulnerabilityKBList(asiaVulnerabilityKBList);

        //漏洞特征对应漏洞
        List<AsiaInfoVulnerabilityEsReport.VulnTagDetail> vulnTagDetailList = buildVulnTagDetailList(duplicateAsiaInfoVulnsList);
        aivr.setVulnTagDetailList(vulnTagDetailList);

        //漏洞级别对应漏洞
        List<AsiaInfoVulnerabilityEsReport.VulnSeverityDetail> vulnSeverityDetails = buildAsiaVulnSeverityList(noDuplicateAsiaInfoVulnsList);
        aivr.setVulnSeverityDetailList(vulnSeverityDetails);

        //设备列表
        List<AsiaInfoVulnerabilityEsReport.VulnDeviceDetail> vulnDeviceDetailList =
                buildVulnDeviceDetailList(asiaInfoDeviceList, duplicateAsiaInfoVulnsList);
        aivr.setVulnDeviceDetailList(vulnDeviceDetailList);

        return BaseResponse.ok(aivr);
    }

    public List<AsiaInfoVuln> getVulnInfo(OtherInfo otherInfoObject, List<Map<String, String>> conditionMap,boolean isDuplicate,List<String> asiaDeviceIdList){

        String noDuplicateVulnSql = buildAsiaInfoVulnSql(asiaDeviceIdList, otherInfoObject, conditionMap,isDuplicate);
        JdbcSqlInfo noDuplicateVulnJdbcSqlInfo = buildSqlParam(noDuplicateVulnSql);
        List<Map<String, Object>> noDuplicateAsiaInfoVuln = bigDataUtil.jdbcQuery(noDuplicateVulnJdbcSqlInfo);

        return ConvertUtil.convertToListOfObjectNoAnnotations(noDuplicateAsiaInfoVuln, AsiaInfoVuln.class);
    }

    public BaseResponse updateReport(String id, String fieldPath, Object value) {
        updateByScript(id, fieldPath, value, AsiaInfoVulnerabilityEsReport.class, null);
        return BaseResponse.ok();
    }

    public BaseResponse asiaInfoDeviceGet(Long eid) {
        OtherInfo otherInfo = getTenantTp(eid);
        if (StringUtils.isEmpty(otherInfo.getTenantTpId())) {
            return BaseResponse.ok(new ArrayList<>(0));
        }
        AiopsDeviceTp aiopsDeviceTp = new AiopsDeviceTp();
        aiopsDeviceTp.setEid(eid);
        aiopsDeviceTp.setTpTenantId(otherInfo.getTenantTpId());
        List<AiopsDeviceTp> aiopsDeviceTpList = aiopsDeviceTpMapper.selectTpModuleInstanceList(aiopsDeviceTp);
        List<AsiaInfoDeviceInfo> asiaInfoDeviceList = aiopsDeviceTpList.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getTpOtherInfo()))
                .map(item -> {
                    AsiaInfoDeviceInfo asiaInfoDeviceInfo = JSONArray.parseObject(item.getTpOtherInfo(), AsiaInfoDeviceInfo.class);
                    String computerName = JSONObject.parseObject(item.getTpOtherInfo()).getString("creator_computer_name");
                    asiaInfoDeviceInfo.setComputerName(computerName);
                    return asiaInfoDeviceInfo;
                })
                .collect(Collectors.toList());
        return BaseResponse.ok(asiaInfoDeviceList);
    }

    public List<AsiaVulnerabilityKB> buildAsiaVulnerabilityKBList(List<AsiaInfoVuln> asiaInfoVulnsList) {
        Map<String, AsiaVulnerabilityKB> vulnDataMap = getVulnData();
        Set<String> vulnTagSet = asiaInfoVulnsList.stream()
                .filter(aiv -> StringUtils.isNotBlank(aiv.getVulnTagString()))
                .flatMap(aiv -> Arrays.stream(aiv.getVulnTagString().split(",")).map(String::valueOf)).collect(Collectors.toSet());
        return vulnTagSet.stream().map(vulnDataMap::get).collect(Collectors.toList());
    }

    private List<AsiaInfoVuln> buildAsiaInfoVulnInfo(List<AsiaInfoVuln> noDupAsiaInfoVulnsList,
                                                     Map<String, AsiaInfoDeviceInfo> deviceInfoMap,List<AsiaInfoVuln> dupAsiaInfoVulnsList) {
        List<AsiaInfoVuln> noDupseverityVulnList = noDupAsiaInfoVulnsList.stream()
                .filter(aiv -> AsiaInfoVulnSeverityStatus.SEVERITY.getCode() == aiv.getSeverity())
                .collect(Collectors.toList());

        Map<String, List<AsiaInfoVuln>> cveNoMap = dupAsiaInfoVulnsList.stream()
                .collect(Collectors.groupingBy(AsiaInfoVuln::getCveNo));

        noDupseverityVulnList.forEach(vuln -> {
            Set<AsiaInfoVulnerabilityEsReport.VulnDeviceDetail> asiaInfoDeviceInfoSet = cveNoMap.get(vuln.getCveNo())
                    .stream()
                    .filter(v -> Objects.nonNull(deviceInfoMap.get(v.getDeviceId())))
                    .map(v -> {
                        AsiaInfoDeviceInfo asiaInfoDeviceInfo = deviceInfoMap.get(v.getDeviceId());
                        AsiaInfoVulnerabilityEsReport.VulnDeviceDetail vulnDeviceDetail =
                                new AsiaInfoVulnerabilityEsReport.VulnDeviceDetail();
                        vulnDeviceDetail.setDeviceId(asiaInfoDeviceInfo.getDeviceId());
                        vulnDeviceDetail.setCreatorComputerName(asiaInfoDeviceInfo.getComputerName());
                        vulnDeviceDetail.setIp(asiaInfoDeviceInfo.getIp());
                        return vulnDeviceDetail;

                    })
                    .collect(Collectors.toSet());

            vuln.getAsiaInfoDeviceInfoList().addAll(asiaInfoDeviceInfoSet);
        });
        return noDupseverityVulnList;
    }


    private List<AsiaInfoVulnerabilityEsReport.VulnDeviceDetail> buildVulnDeviceDetailList(
            List<AsiaInfoDeviceInfo> asiaInfoDeviceList, List<AsiaInfoVuln> asiaInfoVulnsList) {
        Map<String, List<AsiaInfoVuln>> deviceVulnMap = asiaInfoVulnsList.stream()
                .collect(Collectors.groupingBy(AsiaInfoVuln::getDeviceId));

        // 1. 过滤出有漏洞的设备
//        List<AsiaInfoDeviceInfo> filteredDeviceList = asiaInfoDeviceList.stream()
//                .filter(device -> deviceVulnMap.containsKey(device.getDeviceId()))
//                .collect(Collectors.toList());

        // 2. 构建漏洞详细信息列表
        return asiaInfoDeviceList.stream().map(device -> {
            AsiaInfoVulnerabilityEsReport.VulnDeviceDetail deviceDetail = new AsiaInfoVulnerabilityEsReport.VulnDeviceDetail();
            deviceDetail.setDeviceId(device.getDeviceId());
            deviceDetail.setIp(device.getIp());
            deviceDetail.setCreatorComputerName(device.getComputerName());

            List<AsiaInfoVuln> asiaInfoVulnList = deviceVulnMap.getOrDefault(device.getDeviceId(), Collections.emptyList());
            deviceDetail.setVulnTagList(buildAsiaVulnerabilityKBList(asiaInfoVulnList));

            Map<Integer, List<AsiaInfoVuln>> severityMap = asiaInfoVulnList.stream()
                    .collect(Collectors.groupingBy(AsiaInfoVuln::getSeverity));
            deviceDetail.setAsiaInfoSeverityVulnList(severityMap
                    .getOrDefault(AsiaInfoVulnSeverityStatus.SEVERITY.getCode(), Collections.emptyList()));
            deviceDetail.setAsiaInfoHighRiskVulnList(severityMap
                    .getOrDefault(AsiaInfoVulnSeverityStatus.HIGH.getCode(), Collections.emptyList()));

            return deviceDetail;
        }).collect(Collectors.toList());
    }

    public List<AsiaInfoVulnerabilityEsReport.VulnTagDetail> buildVulnTagDetailList(List<AsiaInfoVuln> asiaInfoVulnsList) {
        Map<String, AsiaInfoVulnerabilityEsReport.VulnTagDetail> memoMap = new LinkedHashMap<>();

        asiaInfoVulnsList.stream()
                .filter(vuln -> StringUtils.isNotBlank(vuln.getVulnTagString()))
                .flatMap(vuln -> Arrays.stream(vuln.getVulnTagString().split(","))
                        .map(tag -> new LinkedHashMap.SimpleEntry<>(tag, vuln)))
                .sorted((v1, v2) -> v2.getValue().getSeverity() - v1.getValue().getSeverity())
                .forEach(entry -> {
                    String tag = entry.getKey();
                    AsiaInfoVuln vuln = entry.getValue();
                    String collectedTime = vuln.getCollectedTime();
                    AsiaInfoVulnerabilityEsReport.VulnTagDetail vulnTagDetail = memoMap.computeIfAbsent(tag, t -> {
                        AsiaInfoVulnerabilityEsReport.VulnTagDetail newDetail = new AsiaInfoVulnerabilityEsReport.VulnTagDetail();
                        newDetail.setVulnTag(t);
                        newDetail.setAsiaInfoVulnList(new ArrayList<>());
                        newDetail.setAsiaInfoDeviceInfoList(new HashSet<>());
                        return newDetail;
                    });
                    vulnTagDetail.getAsiaInfoVulnList().add(vuln);
                    vulnTagDetail.getAsiaInfoDeviceInfoList().add(vuln.getDeviceId());
                });

        return new ArrayList<>(memoMap.values());
    }

    public List<AsiaInfoVulnerabilityEsReport.VulnSeverityDetail> buildAsiaVulnSeverityList(List<AsiaInfoVuln> asiaInfoVulnsList) {
        List<AsiaInfoVulnerabilityEsReport.VulnSeverityDetail> ret = new ArrayList<>();
        asiaInfoVulnsList.stream().collect(Collectors.groupingBy(AsiaInfoVuln::getSeverity))
                .forEach((severity, vulnList) -> {
                    AsiaInfoVulnerabilityEsReport.VulnSeverityDetail vulnSeverityDetail =
                            new AsiaInfoVulnerabilityEsReport.VulnSeverityDetail();
                    vulnSeverityDetail.setSeverity(severity);
                    vulnSeverityDetail.setAsiaInfoVulnList(vulnList);
                    ret.add(vulnSeverityDetail);
                });
        return ret;
    }




    private void buildAtpr(AiopsThirdPartyReport atpr) {
        atpr.setId(SnowFlake.getInstance().newId());
        atpr.setReportStatus(ReportStatus.GENERATING.getIndex());
        atpr.setThirdParty(THIRD_PARTY);
        atpr.setProductCode(PRODUCT_CODE);
        atpr.setGenerateTime(LocalDateTime.now());
        atpr.setOtherInfo(JSONObject.toJSONString(buildOtherInfo(atpr)));
        atpr.setSid(Objects.isNull(atpr.getSid()) ? RequestUtil.getHeaderSid() : atpr.getSid());

    }

    private OtherInfo buildOtherInfo(AiopsThirdPartyReport atpr) {

        OtherInfo otherInfo = getTenantTp(atpr.getEid());
        Map<String, String> conditionMap = new HashMap<>();
        conditionMap.put("eid", LongUtil.safeToString(atpr.getEid()));
        conditionMap.put("tpTenantId", otherInfo.getTenantTpId());

        List<AsiaInfoDeviceInfo> asiaInfoDeviceList = getAsiaDevice(atpr.getAsiaDeviceIdList(), conditionMap);
        Map<String, String> deviceTimeMap = asiaInfoDeviceList.stream()
                .collect(Collectors.toMap(
                        AsiaInfoDeviceInfo::getDeviceId,
                        deviceInfo -> Optional.ofNullable(deviceInfo.getLastScanTime()).orElse("1970-01-01 00:00:00") // 将null替换为"默认值"
                ));
        otherInfo.setDeviceTimeMap(deviceTimeMap);

        otherInfo.setAsiaDeviceIdList(atpr.getAsiaDeviceIdList());
        return otherInfo;
    }

    private OtherInfo getTenantTp(Long eid) {
        BaseResponse res = aioUserFeignClient.getAsiaInfoUser(new TenantTpParams(eid, RequestUtil.getHeaderSid()));
        OtherInfo otherInfo = new OtherInfo();
        if (res.checkIsSuccess()) {
            List<Map<String, Object>> dataMap = (List<Map<String, Object>>) res.getData();
            List<TenantTpDto> tenantTpDtoList = ConvertUtil.convertToListOfObjectNoAnnotations(dataMap, TenantTpDto.class);

            if (!tenantTpDtoList.isEmpty()) {
                String tenantTpId = tenantTpDtoList.get(0).getTpTenantId();
                otherInfo.setTenantTpId(tenantTpId);
            }
        }
        return otherInfo;
    }

    private AsiaInfoVulnerabilityEsReport buildAivr(AiopsThirdPartyReport atpr) {
        AsiaInfoVulnerabilityEsReport aivr = new AsiaInfoVulnerabilityEsReport();
        aivr.setReportId(LongUtil.safeToString(atpr.getId()));
        aivr.setId(LongUtil.safeToString(atpr.getId()));
        aivr.setEid(atpr.getEid());
        aivr.setCustomerName(atpr.getCustomerName());
        aivr.setReportName(atpr.getCustomerName() + atpr.getPartReportName());
        aivr.setResult(getReportResult());
        aivr.setReportDate(atpr.getReportDate());
        aivr.setUserName(atpr.getUserName());
        aivr.setAsiaDeviceIdList(atpr.getAsiaDeviceIdList());
        return aivr;
    }

    private JdbcSqlInfo buildSqlParam(String sql) {
        JdbcSqlInfo jdbcSqlInfo = new JdbcSqlInfo();
        jdbcSqlInfo.setSinkType("starrocks");
        jdbcSqlInfo.setSql(sql);
        return jdbcSqlInfo;
    }

    private String buildAsiaInfoVulnSql(List<String> deviceIdList, OtherInfo otherInfo, List<Map<String, String>> additionalConditions,boolean isDuplicate) {
        String table = " servicecloud.AsiaInfoVuln  ";
        String alias = "aiv.";
        StringBuilder sb = new StringBuilder("SELECT ");
        sb.append(alias).append("app_name appName,\n");
        sb.append(alias).append("check_type checkType,\n");
        sb.append(alias).append("create_time createTime,\n");
        sb.append(alias).append("cve_name cveName,\n");
        sb.append(alias).append("cve_no cveNo,\n");
        sb.append(alias).append("cvss cvss,\n");
        sb.append(alias).append("is_linux isLinux,\n");
        sb.append(alias).append("priority priority,\n");
        sb.append(alias).append("severity severity,\n");
        sb.append(alias).append("update_time updateTime,\n");
        sb.append(alias).append("vuln_classification vulnClassification,\n");
        sb.append(alias).append("vuln_tag vulnTagString,\n");
        sb.append(alias).append("cve_solution cveSolution,\n");
        sb.append(alias).append("description description,\n");
        sb.append(alias).append("detection_detail detectionDetail,\n");
        sb.append(alias).append("exp_link expLink,\n");
        sb.append(alias).append("ref_link refLink,\n");
        sb.append(alias).append("collectedTime,\n");
        sb.append(alias).append("last_scan_time lastScanTime,\n");
        sb.append(alias).append("device_id deviceId\n");
        sb.append("FROM ");
        sb.append(table).append(" as aiv \n");
        String otherInfoSql = buildAsiaInfoVulnLastTimeData(otherInfo);
        String duplicateJoinTableAlias = "aiv";
        if (StringUtils.isNotBlank(otherInfoSql)) {
            sb.append(" INNER JOIN (").append(otherInfoSql).append(")");
            sb.append(" as aiv2 ON aiv2.device_id = aiv.device_id and aiv2.last_scan_time = aiv.last_scan_time ");
            duplicateJoinTableAlias = duplicateJoinTableAlias + "2";
        }
        if(isDuplicate){
            sb.append("INNER JOIN (SELECT MAX(last_scan_time) last_scan_time,cve_no FROM servicecloud.AsiaInfoVuln WHERE device_id IN ('");
            sb.append(String.join("','", deviceIdList));
            sb.append("') GROUP BY cve_no) as aiv3 ON aiv3.cve_no = aiv.cve_no AND aiv3.last_scan_time = ")
                    .append(duplicateJoinTableAlias).append(".last_scan_time ");
        }
        String vulnWhere = buildWhere(deviceIdList, additionalConditions, alias);
        sb.append(vulnWhere);
        sb.append(" ORDER BY aiv.severity DESC");
        log.info("[buildAsiaInfoVulnSql] SQL: {}", sb);
        return sb.toString();
    }

    private String buildWhere(List<String> deviceIdList, List<Map<String, String>> additionalConditions, String alias) {
        StringBuilder sb = new StringBuilder();
        sb.append("WHERE 1=1");

        if (!CollectionUtils.isEmpty(deviceIdList)) {
            sb.append(" AND ");
            if (StringUtils.isNotBlank(alias)) {
                sb.append(alias);
            }
            sb.append("device_id IN (");
            for (int i = 0; i < deviceIdList.size(); i++) {
                sb.append("'").append(deviceIdList.get(i)).append("'");
                if (i != deviceIdList.size() - 1) {
                    sb.append(", ");
                }
            }
            sb.append(")");
        }

        // 添加额外的查询条件
        if (Objects.isNull(additionalConditions)) {
            return sb.toString();
        }
        for (Map<String, String> additionalCondition : additionalConditions){
            sb.append(" AND ");
            if (StringUtils.isNotBlank(alias)) {
                sb.append(alias);
            }
            sb.append(additionalCondition.get("key")).append(additionalCondition.get("op")).append(" '").append(additionalCondition.get("value")).append("'");
        }

        return sb.toString();
    }

    private String buildAsiaInfoDeviceSql(List<String> deviceIdList, List<Map<String, String>> additionalConditions) {
        String table = " servicecloud.AsiaInfoDeviceInfo ";
        String alias = "aidi.";
        StringBuilder sb = new StringBuilder("SELECT ");
        sb.append(alias).append("activated_modules activatedModules,\n");
        sb.append(alias).append("architecture ,\n");
        sb.append(alias).append("creator_computer_name computerName ,\n");
        sb.append(alias).append("device_id deviceId,\n");
        sb.append(alias).append("ip ip,\n");
        sb.append(alias).append("login_user loginUser,\n");
        sb.append(alias).append("mac mac,\n");
        sb.append(alias).append("os os,\n");
        sb.append(alias).append("os_type osType,\n");
        sb.append(alias).append("program_version programVersion,\n");
        sb.append(alias).append("register_time registerTime,\n");
        sb.append(alias).append("report_time reportTime,\n");
        sb.append(alias).append("last_scan_time lastScanTime,\n");
        sb.append(alias).append("system_type systemType,\n");
        sb.append(alias).append("status status\n");
        sb.append("FROM   ");
        sb.append(table).append(" as aidi INNER JOIN ");
        sb.append(buildAsiaInfoMaxCollectedTime(table, buildWhere(deviceIdList, additionalConditions, null)));
        sb.append(" as aidi2 ON aidi.device_id = aidi2.device_id and aidi.collectedTime = aidi2.collectedTime ");
        String whereString = buildWhere(deviceIdList, additionalConditions, alias);
        sb.append(whereString);
        sb.append("ORDER BY ").append(alias).append("ip");
        log.info("[buildAsiaInfoDeviceSql] SQL: {}", sb);
        return sb.toString();
    }


    private String buildAsiaInfoMaxCollectedTime(String table, String condition) {
        return " (SELECT max(collectedTime) collectedTime,device_id FROM " + table + " " + condition + "  GROUP BY device_id) ";
    }

    private String buildAsiaInfoVulnMaxCollectedTime(String table, String condition) {
        return " (SELECT max(last_scan_time) last_scan_time,cve_no,app_name,device_id FROM " + table + " " + condition +
                "  GROUP BY cve_no,app_name,device_id) ";
    }

    private String buildAsiaInfoVulnLastTimeData(OtherInfo otherInfo) {
        if (Objects.isNull(otherInfo) || CollectionUtils.isEmpty(otherInfo.getDeviceTimeMap())) {
            return null;
        }
        Map<String, String> deviceTimeMap = otherInfo.getDeviceTimeMap();
        StringJoiner joiner = new StringJoiner(" UNION ALL ");
        deviceTimeMap.forEach((device, time) -> {
            if (StringUtils.isNotBlank(time) && !"null".equals(time)) {
                String selectClause = String.format("SELECT '%s' as device_id, '%s' as last_scan_time ", device, time);
                joiner.add(selectClause);
            }
        });
        return joiner.toString();
    }

    private Map<String, AsiaVulnerabilityKB> getVulnData() {
        return asiaVulnerabilityMapper.selectVulnTag().stream().collect(Collectors.toMap(AsiaVulnerabilityKB::getVulnTag
                , Function.identity()));
    }

    private List<AsiaInfoDeviceInfo> getAsiaDevice(List<String> asiaDeviceIdList, Map<String, String> conditionMap) {

        Map<String, Object> condition = new HashMap<>();
        condition.put("eid",conditionMap.get("eid"));
        condition.put("tpTenantId",conditionMap.get("tpTenantId"));
        condition.put("tpDeviceIds", asiaDeviceIdList);
        //todo
        List<AiopsDeviceTp> aiopsDeviceTpList = aiopsDeviceTpMapper.selectAiopsDeviceTpByDeviceIds(condition);
        List<AsiaInfoDeviceInfo> asiaInfoDeviceList = aiopsDeviceTpList.stream()
                .map(item -> {
                    AsiaInfoDeviceInfo asiaInfoDeviceInfo = JSONArray.parseObject(item.getTpOtherInfo(), AsiaInfoDeviceInfo.class);
                    String computerName = JSONObject.parseObject(item.getTpOtherInfo()).getString("creator_computer_name");
                    asiaInfoDeviceInfo.setComputerName(computerName);
                    return asiaInfoDeviceInfo;
                })
                .collect(Collectors.toList());
        return asiaInfoDeviceList;
    }

    private String getReportResult() {
        return serviceArea.equals("CN") ? "<p><font color=\"#488FFF\">漏洞运维建议： </font></p><p>1. 建议及时修复，否则有漏洞被利用导致业务中断的风险，修复先后顺序按“漏洞分级“里的严重性，从高到低，按主机上运行业务的重要程度，从高到低，采取修复措施，若因为业务连续性要求无法更新补丁，可参考鼎捷安全防护解决方案包，对系统进行加固；</p><p>2. 建议立即修复严重和高危级别的漏洞，中危级别通过指定漏洞修复计划，分阶段修复。   </p><p><br/></p><p><font color=\"#488FFF\">漏洞治理方案建议： </font></p><p>1. 除了及时更新操作系统和应用软件版本或应用相关补丁以外，建议部署终端检测与响应（EDR）产品，实时监控终端设备，检测异常行为并及时阻断漏洞利用； </p><p>2. 建议部署病毒防护类产品，保持病毒库及时更新，并开启实时防护或周期性病毒扫描，及时检测和清除恶意软件，有效防范恶意代码利用，权限提升等漏洞利用；</p><p>3. 更多全面防护，参见鼎捷安全防护解决方案包。</p>\u200B"
                : "<p><font color=\"#488FFF\">漏洞運維建議： </font></p><p>1. 建議及時修復，否則有漏洞被利用導致業務中斷的風險，修復先後順序按“漏洞分級“里的嚴重性，從高到低，按主機上運行業務的重要程度，從高到低，採取修復措施，若因為業務連續性要求無法更新補丁，可參考鼎捷安全防護解決方案包，對系統進行加固；</p><p>2. 建議立即修復嚴重和高危級別的漏洞，中危級別通過指定漏洞修復計劃，分階段修復。   </p><p><br/></p><p><font color=\"#488FFF\">漏洞治理方案建議： </font></p><p>1. 除了及時更新操作系統和應用軟件版本或應用相關補丁以外，建議部署終端檢測與響應（EDR）產品，實時監控終端設備，檢測異常行為並及時阻斷漏洞利用； </p><p>2. 建議部署病毒防護類產品，保持病毒庫及時更新，並開啟實時防護或周期性病毒掃描，及時檢測和清除惡意軟件，有效防範惡意代碼利用，權限提升等漏洞利用；</p><p>3. 更多全面防護，參見鼎捷安全防護解決方案包。</p>\u200B";
    }

}
