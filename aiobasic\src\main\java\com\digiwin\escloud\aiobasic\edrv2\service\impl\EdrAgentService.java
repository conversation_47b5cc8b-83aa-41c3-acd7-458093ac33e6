package com.digiwin.escloud.aiobasic.edrv2.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;
import com.digiwin.escloud.aiobasic.edr.model.edr.EdrOrgCollectorProcessRecordSaveDTO;
import com.digiwin.escloud.aiobasic.edr.service.edr.impl.EdrReportService;
import com.digiwin.escloud.aiobasic.edrv2.constant.Edrv2Const;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrAgentMapper;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrScanPlanMapper;
import com.digiwin.escloud.aiobasic.edrv2.model.*;
import com.digiwin.escloud.aiobasic.edrv2.service.IEdrAgentService;
import com.digiwin.escloud.aiobasic.edrv2.service.IEdrScanPlanService;
import com.digiwin.escloud.aiobasic.edrv2.utils.*;
import com.digiwin.escloud.aiobasic.mail.service.EdrRemoveIdentity;
import com.digiwin.escloud.aiobasic.report.model.EdrAgentField;
import com.digiwin.escloud.aiobasic.util.BigDataUtil;
import com.digiwin.escloud.aiobasic.util.EasyExcelUtil;
import com.digiwin.escloud.aiobasic.util.MessageUtils;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.model.excel.NoModelWriteData;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.reactivex.Observable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;

import static com.digiwin.escloud.aiobasic.edrv2.constant.Constants.*;
import static com.digiwin.escloud.aiobasic.edrv2.constant.Edrv2Const.*;

@Service
public class EdrAgentService implements IEdrAgentService, ParamCheckHelp {
    private static final Logger log = LoggerFactory.getLogger(EdrAgentService.class);
    @Value("${ENVIRONMENT:dev}")
    private String environment;
    @Autowired
    EdrAgentMapper edrAgentMapper;
    @Autowired
    EdrScanPlanMapper edrScanPlanMapper;
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    EdrReportService edrReportService;
    @Autowired
    Edrv2Util edrv2Util;
    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Autowired
    EasyExcelUtil easyExcelUtil;
    @Autowired
    MessageUtils messageUtils;
    @Autowired
    EdrRemoveIdentity edrRemoveIdentity;
    @Autowired
    TransactionTemplate transactionTemplate;
    @Autowired
    IEdrScanPlanService edrScanPlanService;
    @Autowired
    private BigDataUtil bigDataUtil;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").withZone(ZoneId.of("Etc/GMT")); // 設置美國時區

    @Override
    public BaseResponse getAgentList(List<String> eidList, EdrAgentParam edrAgentParam) {
        edrAgentParam.setEidList(eidList);
        List<EdrAgentField> agentList = edrAgentMapper.getAgent(edrAgentParam);

        // 過濾掉已經處理過的，剩下的才進行 cron 計算
        agentList = getNextExecutionTime(agentList);

        // 根據條件分組查詢
        Map<String, List<EdrAgentField>> collect = agentList.stream()
                .collect(
                        Collectors.groupingBy(new Function<EdrAgentField, String>() {
                            @Override
                            public String apply(EdrAgentField field) {
                                return SERVERID + "$$" + field.getSiteName() + "$$" + field.getGroupTitle();
                            }
                        }, Collectors.toList()));

        Map<String, Object> collectorList = new HashMap<>(collect);
        collectorList.put("updateTime", stringRedisTemplate.opsForValue().get("Agent_Sync_Time_" + eidList.get(0)));

        return BaseResponse.ok(collectorList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse saveAgent(List<EdrAgentField> edrAgentFieldRequest) {
        // 更新同步時間
        String eid = edrAgentFieldRequest.stream().map(EdrAgentField::getEid).findFirst().orElse(null);
        if (StringUtil.isNotEmpty(eid)) {
            stringRedisTemplate.opsForValue().set("Agent_Sync_Time_" + eid, DateUtil.getSomeDateFormatString(DateUtil.getLocalNow(), DateUtil.DATE_TIME_FORMATTER));
        }

        // 檢查同步數據是否不存在於原廠
        String siteId = edrAgentFieldRequest.stream().map(EdrAgentField::getSiteId).findFirst().orElse(null);

        if (StringUtil.isEmpty(siteId)) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, "siteId");
        }

        EdrAgentParam edrAgentParam = new EdrAgentParam(siteId);

        List<EdrAgentField> agentList = edrAgentMapper.getAgent(edrAgentParam);

        // 取得不在最新清單中的agentId
        List<String> finalEdrAgentList = edrAgentFieldRequest.stream().map(EdrAgentField::getAgentId).collect(Collectors.toList());
        List<String> idsToDelete = agentList.stream()
                .map(EdrAgentField::getAgentId)
                .filter(agentId -> !finalEdrAgentList.contains(agentId))
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(idsToDelete)) {
            edrAgentMapper.removeAgent(LongUtil.objectToLong(siteId), null, idsToDelete);

            // 同步刪除SR數據
            removeSRAgent(LongUtil.objectToLong(siteId), idsToDelete);
        }

        // 檢查排程狀態變更，壓紀錄
        scanStatusExecRecord(agentList, edrAgentFieldRequest);

        // 檢查設備組件狀態啟用，壓紀錄
        operationalStateEnableRecord(agentList, edrAgentFieldRequest);

        edrAgentFieldRequest.forEach(edrAgentField -> {
            // 設置id值
            edrAgentField.setId(SnowFlake.getInstance().newIdStr());

            // 更新為待移除的設備要押PendingUninstallTime
            agentList.stream()
                    .filter(agent -> edrAgentField.getAgentId().equals(agent.getAgentId())
                            && Boolean.TRUE.equals(edrAgentField.getIsPendingUninstall())
                            && Boolean.FALSE.equals(agent.getIsPendingUninstall()))
                    .findFirst()
                    .ifPresent(agent -> edrAgentField.setPendingUninstallTime(new Date()));
        });

        // 檢查每一項結果是否為0
        boolean anyFailed = edrAgentFieldRequest.stream()
                .mapToInt(edrAgentMapper::upsertAgent) // 這邊會返回影響行數
                .anyMatch(rowsAffected -> rowsAffected == 0);

        if (anyFailed) {
            return BaseResponse.error(ResponseCode.EDR_UPDATE_AGENT_FAILED);
        }

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse getOsTotalAmount(String eid, Date timeFrom, Date timeTo) {
        List<Map<String, Object>> agentList = edrAgentMapper.getOSTotalAmount(eid, timeFrom, timeTo);

        return BaseResponse.ok(agentList);
    }

    @Override
    public BaseResponse enableOrDisableAgent(String state, EdrAgentEnableParam edrAgentEnableParam) {
        // 檢查agent運行狀態
        List<EdrAgentField> edrAgentFieldList = edrAgentMapper.getAgentByAgentId(edrAgentEnableParam.getSiteId(), edrAgentEnableParam.getAgentIds());
        List<EdrAgentField> offlineAgentList = edrAgentFieldList.stream()
                .filter(edrAgentField -> OFFLINE.equalsIgnoreCase(edrAgentField.getManagmentConnectivity()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(offlineAgentList)) {
            if (DISABLE.equals(state)) {
                return disableAgent(edrAgentEnableParam);
            }
            return enableAgent(edrAgentEnableParam);
        }
        return BaseResponse.error(ResponseCode.EDR_AGENT_IS_OFFLINE, offlineAgentList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse removeAgent(EdrAgentDeleteParam param) {
        // 取得設備資訊
        EdrAgentField agent = edrAgentMapper.getAgentByAgentId(param.getSiteId(), Collections.singletonList(param.getAgentId())).stream().findFirst().orElse(null);
        if (Objects.isNull(agent)) {
            return BaseResponse.error(ResponseCode.EDR_AGENT_FOUND_ZERO);
        }

        // 檢查設備是否離線
        if (ONLINE.equals(agent.getManagmentConnectivity())) {
            log.error("Only devices that have been offline can be deleted.");
            return BaseResponse.error(ResponseCode.EDR_AGENT_IS_ONLINE);
        }

        // 刪除同步數據
        if (!(edrAgentMapper.removeAgent(LongUtil.objectToLong(param.getSiteId()), param.getAgentId(), null) > 0)) {
            log.error("Agent remove sync data failed.");
            return BaseResponse.error(ResponseCode.EDR_AGENT_REMOVE_SYNC_DATA_FAILED);
        }

        // 實際刪除設備
        if (!removeS1Agent(param.getSiteId(), param.getAgentId())) {
            throw new RuntimeException("Remove S1 Agent Failed");
        }

        //操作紀錄Log
        EdrOrgCollectorProcessRecordSaveDTO record = new EdrOrgCollectorProcessRecordSaveDTO();
        Long eid = Optional.ofNullable(LongUtil.objectToLong(param.getEid())).orElse(RequestUtil.getHeaderEid());
        record.setEid(eid);
        record.setOrgId(LongUtil.objectToLong(param.getSiteId()));
        record.setServerId(SERVERID);
        record.setOrganization(param.getOrganization());
        record.setCollectorId(LongUtil.objectToLong(param.getAgentId()));
        record.setUserId(param.getUserId());
        record.setUserName(param.getUserName());
        record.setOperation(DELETE);
        record.setOriginalGroup(param.getOriginalGroup());
        record.setDeleteReason(param.getDeleteReason());
        record.setAgentChangeReason(agent.getEndpointName() + "," + agent.getLastReportedIP());
        edrReportService.saveProcessRecord(record);

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse getOsNameList(Long eid) {
        List<String> osNameList = edrAgentMapper.getOsNameList(eid);
        return BaseResponse.ok(osNameList);
    }

    @Override
    public BaseResponse syncAgent(String eid, List<String> siteIds) throws Exception {
        // region 參數驗證
        Optional<BaseResponse> opt = checkParamIsEmpty(siteIds, "siteIds");
        if (opt.isPresent()) {
            return opt.get();
        }
        // endregion

        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("siteIds", siteIds);
        requestBody.put("env", environment);

        // 将 Map 轉換為 JSON 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequestBody = objectMapper.writeValueAsString(requestBody);

        // 發送請求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(jsonRequestBody, headers);
        ResponseEntity<String> response = restTemplate.exchange(Edrv2Const.SYNC_AGENT_URI, HttpMethod.POST, request, String.class);

        // 更新同步時間
        stringRedisTemplate.opsForValue().set("Agent_Sync_Time_" + eid, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        return BaseResponse.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse scan(EdrScan edrScan) throws Exception {
        // region 參數檢查
        List<String> params = Stream.of(
                        new AbstractMap.SimpleEntry<>("agentIds", checkParamIsEmpty(edrScan.getAgentIds(), "agentIds")),
                    new AbstractMap.SimpleEntry<>("siteIds", checkParamIsEmpty(edrScan.getSiteIds(), "siteIds")),
                        new AbstractMap.SimpleEntry<>("executeAction", checkParamIsEmpty(edrScan.getExecuteAction(), "executeAction")))
                .filter(param -> param.getValue().isPresent())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(params)) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, String.join(", ", params));
        }
        // endregion

        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("data", new HashMap<>());

        Map<String, List<String>> filterMap = new HashMap<>();
        filterMap.put("siteIds", edrScan.getSiteIds());
        filterMap.put("ids", edrScan.getAgentIds());

        requestBody.put("filter", filterMap);

        // 将 Map 轉換為 JSON 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequestBody = objectMapper.writeValueAsString(requestBody);

        // 根據數據類型整理數據
        String executeAction = edrScan.getExecuteAction();
        String uri = ON.equals(executeAction) ? Edrv2Const.INITIATE_SCAN_URI : Edrv2Const.ABORT_SCAN_URI;
        String scanStatus = ON.equals(executeAction) ? "In progress" : "Aborted";

        // 數據更新至資料庫檢查
        Integer updateCount = edrAgentMapper.updateAgentScanStatus(edrScan.getSiteIds(), edrScan.getAgentIds(), scanStatus);
        if (updateCount != edrScan.getAgentIds().size()) {
            return BaseResponse.error(ON.equals(executeAction) ? ResponseCode.EDR_SCAN_FAILED : ResponseCode.EDR_SCAN_ABORT_FAILED);
        }

        // 發送請求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", Edrv2Const.TOKEN);
        HttpEntity<String> request = new HttpEntity<>(jsonRequestBody, headers);
        ResponseEntity<JSONObject> response = restTemplate.exchange(uri, HttpMethod.POST, request, JSONObject.class);

        // 解析數據
        JSONObject responseBody = response.getBody();
        if (Objects.isNull(responseBody)) {
            throw new RuntimeException("Scan failed: Response body is null");
        }

        JSONObject res = responseBody.getJSONObject("data");
        // 數據結果檢查，若失敗則拋出異常觸發回滾
        if (Objects.isNull(res) || !res.containsKey("affected") || !res.get("affected").equals(edrScan.getAgentIds().size())) {
            throw new RuntimeException("Scan failed: Affected agents mismatch");
        }

        // 取得排程 START 的 sstaId 及 檢查是否有已有成對紀錄
        if (OFF.equals(edrScan.getExecuteAction())) {
            List<EdrScanPlanAgent> scanPlanAgentList = edrScanPlanMapper.getScanPlanAgentByAgentId(edrScan.getAgentIds());
            Set<Long> abortableSsaIdSet = scanPlanAgentList.stream().map(EdrScanPlanAgent::getId).collect(Collectors.toSet());

            abortableSsaIdSet.forEach(ssaId -> {
                EdrScanPlanAgentActivity activity = edrScanPlanMapper.getStartAgentActivity(ssaId);
                if (Objects.isNull(activity) || LongUtil.isEmpty(activity.getSstaId()) || activity.getActivityCount() >= 2) {
                    return;
                }
                // 將原進行中排程押上中止紀錄
                String deleteReason = StringUtil.isNotEmpty(edrScan.getDeleteReason()) ? edrScan.getDeleteReason() : MEMO_ABORTED;
                edrScanPlanService.execRecord(Collections.singletonList(ssaId), ABORTED, deleteReason, activity.getSstaId());
            });
        }

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse sendRemoveIdentity(EdrIdentitySend edrIdentitySend) throws Exception {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(edrIdentitySend, "edrIdentitySend");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        List<EdrAgentField> edrAgent = edrAgentMapper.getAgentByAgentId(edrIdentitySend.getSiteId(), Collections.singletonList(edrIdentitySend.getAgentId()));
        if (edrAgent.isEmpty()) {
            return BaseResponse.error(ResponseCode.EDR_AGENT_FOUND_ZERO);
        }

        Boolean isPendingUninstall = edrAgent.get(0).getIsPendingUninstall();
        if (!isPendingUninstall) {
            return BaseResponse.error(ResponseCode.EDR_AGENT_IS_NOT_PENDING_UNINSTALL);
        }

        edrRemoveIdentity.sendMail(edrIdentitySend);
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse uninstall(EdrUninstall edrUninstall) throws Exception {
        //判斷是否是要取消移除
        if("Y".equals(edrUninstall.getState())) {
            return removeUninstall(edrUninstall);
        }

        // region 參數檢查
        List<String> params = Stream.of(
                        new AbstractMap.SimpleEntry<>("eid", LongUtil.isEmpty(edrUninstall.getEid())),
                        new AbstractMap.SimpleEntry<>("siteId", StringUtil.isEmpty(edrUninstall.getSiteId())),
                        new AbstractMap.SimpleEntry<>("agentId", StringUtil.isEmpty(edrUninstall.getAgentId())),
                        new AbstractMap.SimpleEntry<>("userId", StringUtil.isEmpty(edrUninstall.getUserId())),
                        new AbstractMap.SimpleEntry<>("userName", StringUtil.isEmpty(edrUninstall.getUserName())),
                        new AbstractMap.SimpleEntry<>("removeReason", StringUtil.isEmpty(edrUninstall.getRemoveReason())),
                        new AbstractMap.SimpleEntry<>("sendTime", StringUtil.isEmpty(edrUninstall.getSendTime())))
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(params)) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, String.join(", ", params));
        }
        // endregion

        // 判斷是否在5分鐘內
        Date now = new Date();
        Date dateTime = DateUtils.parseDate(edrUninstall.getSendTime(), "yyyy-MM-dd HH:mm:ss");
        Duration duration = Duration.between(dateTime.toInstant(), now.toInstant());
        if (duration.toMillis() > 5 * 60 * 1000) {
            return BaseResponse.error(ResponseCode.EDR_UNINSTALL_IDENTITY_TIMEOUT);
        }

        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("data", new HashMap<>());
        Map<String, List<String>> filterMap = new HashMap<>();
        filterMap.put("siteIds", Collections.singletonList(edrUninstall.getSiteId()));
        filterMap.put("ids", Collections.singletonList(edrUninstall.getAgentId()));
        requestBody.put("filter", filterMap);

        // 解析數據
        Map<String, Object> res = edrv2Util.sendRequest(APPROVE_UNINSTALL, HttpMethod.POST, requestBody);
        Object object = res.getOrDefault("error", null);
        boolean isFailed = Objects.nonNull(object) || (res == null || !res.containsKey("affected") || !res.get("affected").equals(1));
        String operation = isFailed ? REMOVE_FAILED : REMOVE_SUCCESS;
        String agentChangeReason = isFailed ? MEMO_FAILED : edrUninstall.getRemoveReason();

        Boolean executeRes = transactionTemplate.execute(transactionStatus -> {
            try {
                // 針對設備做同步
                if (!isFailed) {
                    Optional.ofNullable(edrAgentMapper.updateIsUninstalled(edrUninstall.getSiteId(), edrUninstall.getAgentId()))
                            .filter(tasksAffectedRow -> tasksAffectedRow > 0)
                            .orElseThrow(() -> new RuntimeException("EDR2.0 AGENT UPDATE IS_UNINSTALLED FAILED"));
                }

                List<EdrAgentField> edrAgent = edrAgentMapper.getAgentByAgentId(edrUninstall.getSiteId(), Collections.singletonList(edrUninstall.getAgentId()));
                EdrOrgCollectorProcessRecordSaveDTO recode = new EdrOrgCollectorProcessRecordSaveDTO();
                recode.setEid(edrUninstall.getEid());
                recode.setOrgId(LongUtil.objectToLong(edrUninstall.getSiteId()));
                recode.setCollectorId(LongUtil.objectToLong(edrUninstall.getAgentId()));
                recode.setOperation(operation);
                recode.setOrganization(edrAgent.get(0).getSiteName());
                recode.setServerId(SERVERID);
                recode.setUserId(edrUninstall.getUserId());
                recode.setUserName(edrUninstall.getUserName());
                recode.setEnable(0);
                recode.setAgentChangeReason(edrAgent.get(0).getEndpointName() + "," + edrAgent.get(0).getLastReportedIP());
                recode.setDeleteReason(agentChangeReason);

                // 移除記錄留存
                Optional.ofNullable(edrReportService.saveProcessRecords(Collections.singletonList(recode)))
                        .filter(tasksAffectedRow -> tasksAffectedRow > 0)
                        .orElseThrow(() -> new RuntimeException("EDR2.0 AGENT SAVE PROCESS RECORD FAILED"));
            } catch (Exception e) {
                transactionStatus.setRollbackOnly();
                log.error("EDR2.0 AGENT UNINSTALL FAILED", e);
                return false;
            }
            return true;
        });
        if (!executeRes || isFailed) {
            if (Objects.nonNull(object)) {
                return BaseResponse.error(ResponseCode.EDR_UNINSTALL_FAILED, object);
            }
            return BaseResponse.error(ResponseCode.EDR_UNINSTALL_FAILED);
        }

        return BaseResponse.ok();
    }

    private BaseResponse removeUninstall(EdrUninstall edrUninstall) {
        // region 參數檢查
        List<String> params = Stream.of(
                        new AbstractMap.SimpleEntry<>("eid", LongUtil.isEmpty(edrUninstall.getEid())),
                        new AbstractMap.SimpleEntry<>("siteId", StringUtil.isEmpty(edrUninstall.getSiteId())),
                        new AbstractMap.SimpleEntry<>("agentId", StringUtil.isEmpty(edrUninstall.getAgentId())),
                        new AbstractMap.SimpleEntry<>("userId", StringUtil.isEmpty(edrUninstall.getUserId())),
                        new AbstractMap.SimpleEntry<>("userName", StringUtil.isEmpty(edrUninstall.getUserName())),
                        new AbstractMap.SimpleEntry<>("cancelReason", StringUtil.isEmpty(edrUninstall.getCancelReason())))
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(params)) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, String.join(", ", params));
        }
        // endregion
        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("data", new HashMap<>());
        Map<String, List<String>> filterMap = new HashMap<>();
        filterMap.put("siteIds", Collections.singletonList(edrUninstall.getSiteId()));
        filterMap.put("ids", Collections.singletonList(edrUninstall.getAgentId()));
        requestBody.put("filter", filterMap);

        // 解析數據
        Map<String, Object> res = edrv2Util.sendRequest(REJECT_UNINSTALL, HttpMethod.POST, requestBody);
        Object object = res.getOrDefault("error", null);
        boolean isCancelSuccess;
        if (ObjectUtil.isEmpty(res)  || Objects.nonNull(object) || !res.containsKey("affected") || !res.get("affected").equals(1)){
           isCancelSuccess = false;
        } else {
            isCancelSuccess = true;
        }

        Boolean executeRes = transactionTemplate.execute(transactionStatus -> {
            try {
                if(isCancelSuccess) {
                    // 針對設備做同步
                    Optional.ofNullable(edrAgentMapper.updateIsPendingUninstall(edrUninstall.getSiteId(), edrUninstall.getAgentId()))
                            .filter(tasksAffectedRow -> tasksAffectedRow > 0)
                            .orElseThrow(() -> new RuntimeException("EDR2.0 AGENT UPDATE IS_UNINSTALLED FAILED"));
                }
                String operation = isCancelSuccess ? CANCEL_REMOVE_SUCCESS : CANCEL_REMOVE_FAILED;
                String agentChangeReason = isCancelSuccess ? edrUninstall.getCancelReason() : MEMO_FAILED;

                List<EdrAgentField> edrAgent = edrAgentMapper.getAgentByAgentId(edrUninstall.getSiteId(), Collections.singletonList(edrUninstall.getAgentId()));
                EdrOrgCollectorProcessRecordSaveDTO recode = new EdrOrgCollectorProcessRecordSaveDTO();
                recode.setEid(edrUninstall.getEid());
                recode.setOrgId(LongUtil.objectToLong(edrUninstall.getSiteId()));
                recode.setCollectorId(LongUtil.objectToLong(edrUninstall.getAgentId()));
                recode.setOperation(operation);
                recode.setOrganization(edrAgent.get(0).getSiteName());
                recode.setServerId(SERVERID);
                recode.setUserId(edrUninstall.getUserId());
                recode.setUserName(edrUninstall.getUserName());
                recode.setEnable(0);
                recode.setAgentChangeReason(agentChangeReason);

                // 取消移除記錄留存
                Optional.ofNullable(edrReportService.saveProcessRecords(Collections.singletonList(recode)))
                        .filter(tasksAffectedRow -> tasksAffectedRow > 0)
                        .orElseThrow(() -> new RuntimeException("EDR2.0 AGENT SAVE PROCESS RECORD FAILED"));
            } catch (Exception e) {
                transactionStatus.setRollbackOnly();
                log.error("EDR2.0 AGENT CANCEL UNINSTALL FAILED", e);
                return false;
            }
            return true;
        });
        if (!executeRes || !isCancelSuccess) {
            return BaseResponse.error(ResponseCode.EDR_CANCEL_UNINSTALL_FAILED);

        }

        return BaseResponse.ok();
    }

    private BaseResponse syncAgentByAgentId(String environment, List<Map<String, Object>> agentInfo) throws Exception {
        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("agentInfo", agentInfo);
        requestBody.put("env", environment);

        // 将 Map 轉換為 JSON 字符串
        String jsonRequestBody = new JSONObject(requestBody).toString();

        // 發送請求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(jsonRequestBody, headers);
        ResponseEntity<String> response = restTemplate.exchange(Edrv2Const.SYNC_AGENT_BY_AGENTID_URI, HttpMethod.POST, request, String.class);

        return BaseResponse.ok();
    }

    private BaseResponse enableAgent(EdrAgentEnableParam edrAgentEnableParam) {

        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("shouldReboot", "false");

        Map<String, Object> filterMap = new HashMap<>();
        filterMap.put("siteIds", edrAgentEnableParam.getSiteId());
        filterMap.put("ids", edrAgentEnableParam.getAgentIds());

        requestBody.put("data", dataMap);
        requestBody.put("filter", filterMap);

        // 發送請求
        Map<String, Object> res = edrv2Util.sendRequest(ENABLE_AGENT_URI, HttpMethod.POST, requestBody);

        Object object = res.getOrDefault("error", null);
        if (Objects.nonNull(object)){
            return BaseResponse.error(ResponseCode.EDR_AGENT_ENABLE_FAILED, object);
        }
        if (res.getOrDefault("affected", 0).equals(0)) {
            return BaseResponse.error(ResponseCode.EDR_AGENT_ENABLE_FAILED);
        }

        // 更新agent狀態及關閉時間
        edrAgentMapper.updateAgentStatus("Not disabled", null, edrAgentEnableParam.getAgentIds());

        return BaseResponse.ok(res);
    }

    private BaseResponse disableAgent(EdrAgentEnableParam edrAgentEnableParam) {
        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("shouldReboot", "false");

        if (edrAgentEnableParam.getExpiration() != null) {
            ZonedDateTime zonedDateTime = Instant.ofEpochMilli(edrAgentEnableParam.getExpiration().getTime())
                    .atZone(ZoneId.of("GMT+08:00")); // 使用 GMT+08:00
            // 轉換時間格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
            String expiration = zonedDateTime.format(formatter);

            if (!expiration.isEmpty()) {
                dataMap.put("expiration", expiration);
                dataMap.put("expirationTimezone", "GMT+08:00");
            }
        }


        Map<String, Object> filterMap = new HashMap<>();
        filterMap.put("siteIds", edrAgentEnableParam.getSiteId());
        filterMap.put("ids", edrAgentEnableParam.getAgentIds());

        requestBody.put("data", dataMap);
        requestBody.put("filter", filterMap);

        // 發送請求
        Map<String, Object> res = edrv2Util.sendRequest(DISABLE_AGENT_URI, HttpMethod.POST, requestBody);

        if (dataMap.containsKey("expiration")) {
            res.put("expiration", dataMap.get("expiration"));
            res.put("expirationTimezone", dataMap.get("expirationTimezone"));
        }

        Object object = res.getOrDefault("error", null);
        if (Objects.nonNull(object)){
            return BaseResponse.error(ResponseCode.EDR_AGENT_DISABLE_FAILED, object);
        }
        if (res.getOrDefault("affected", 0).equals(0)) {
            return BaseResponse.error(ResponseCode.EDR_AGENT_DISABLE_FAILED);
        }

        // 更新agent狀態及關閉時間
        edrAgentMapper.updateAgentStatus("Disabled and not rebooted by the user", new Date(), edrAgentEnableParam.getAgentIds());

        return BaseResponse.ok(res);
    }

    private Boolean removeS1Agent(String siteId, String agentId) {

        // 建立Body數據
        Map<String, Object> requestBody = new HashMap<>();

        Map<String, Object> filterMap = new HashMap<>();
        filterMap.put("siteIds", siteId);
        filterMap.put("ids", Collections.singletonList(agentId));

        requestBody.put("data", new HashMap<>());
        requestBody.put("filter", filterMap);

        // 發送請求
        Map<String, Object> res = edrv2Util.sendRequest(REMOVE_AGENT_URI, HttpMethod.POST, requestBody);

        Object object = res.getOrDefault("error", null);
        if (Objects.nonNull(object)){
            log.error("remove agent failed: ", object);
            return false;
        }
        Integer affected = IntegerUtil.objectToInteger(res.getOrDefault("affected", 0));

        return affected > 0;
    }

    @Override
    public BaseResponse getConnectivityTotalAmount(Long eid, Date timeFrom, Date timeTo) {
        // 處理時間格式，轉為美國時區
        String timeFromString = "";
        String timeToString = "";
        if (Objects.nonNull(timeFrom)) {
            ZonedDateTime zonedDateTimeFrom = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timeFrom.getTime()), ZoneId.of("Etc/GMT"));
            timeFromString = zonedDateTimeFrom.format(FORMATTER);
        }
        if (Objects.nonNull(timeTo)) {
            ZonedDateTime zonedDateTimeTo = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timeTo.getTime()), ZoneId.of("Etc/GMT"));
            timeToString = zonedDateTimeTo.format(FORMATTER);
        }

        // 將siteIdsList轉為String
        List<String> siteIdsList = edrAgentMapper.getAgentSiteIdByEid(eid).stream()
                .map(map -> map.getOrDefault("siteId", "").toString())
                .collect(Collectors.toList());
        if (siteIdsList.isEmpty()) {
            return BaseResponse.ok();
        }
        String siteIds = String.join(",", siteIdsList);

        // 解析數據 - 運行中
        List<Map<String, Object>> responseBody = new ArrayList<>();
        Map<String, Object> onlineGroup = new HashMap<>();
        onlineGroup.put("title", "Online");
        Map<String, Object> onlineData = (Map<String, Object>) edrv2Util.getOnlineTotalAmount(siteIds,timeFromString,timeToString).getData();
        if (!onlineData.isEmpty()) {
            onlineGroup.put("count", onlineData.get("total"));
        }
        responseBody.add(onlineGroup);

        // 解析數據 - 已關閉
        Map<String, Object> offlineGroup = new HashMap<>();
        offlineGroup.put("title", "Offline");
        Map<String, Object> offlineData = (Map<String, Object>) edrv2Util.getOfflineTotalAmount(siteIds,timeFromString,timeToString).getData();
        if (!offlineData.isEmpty()) {
            offlineGroup.put("count", offlineData.get("total"));
        }
        responseBody.add(offlineGroup);

        // 回傳
        return BaseResponse.ok(responseBody);

    }

    @Override
    public BaseResponse getHealthTotalAmount(Long eid, Date timeFrom, Date timeTo) {
        // 處理時間格式，轉為美國時區
        String timeFromString = "";
        String timeToString = "";
        if (Objects.nonNull(timeFrom)) {
            ZonedDateTime zonedDateTimeFrom = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timeFrom.getTime()), ZoneId.of("Etc/GMT"));
            timeFromString = zonedDateTimeFrom.format(FORMATTER);
        }
        if (Objects.nonNull(timeTo)) {
            ZonedDateTime zonedDateTimeTo = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timeTo.getTime()), ZoneId.of("Etc/GMT"));
            timeToString = zonedDateTimeTo.format(FORMATTER);
        }

        // 將siteIdsList轉為String
        List<String> siteIdsList = edrAgentMapper.getAgentSiteIdByEid(eid).stream()
                .map(map -> map.getOrDefault("siteId", "").toString())
                .collect(Collectors.toList());
        if (siteIdsList.isEmpty()) {
            return BaseResponse.ok();
        }
        String siteIds = String.join(",", siteIdsList);

        // 解析數據 - 不健康
        List<Map<String, Object>> responseBody = new ArrayList<>();
        Map<String, Object> infectedGroup = new HashMap<>();
        infectedGroup.put("title", "Infected");
        Map<String, Object> infectedData = (Map<String, Object>) edrv2Util.getInfectedTotalAmount(siteIds,timeFromString,timeToString).getData();
        if (!infectedData.isEmpty()) {
            infectedGroup.put("count", infectedData.get("total"));
        }
        responseBody.add(infectedGroup);

        // 解析數據 - 健康
        Map<String, Object> healthGroup = new HashMap<>();
        healthGroup.put("title", "Healthy");
        Map<String, Object> healthData = (Map<String, Object>) edrv2Util.getHealthTotalAmount(siteIds,timeFromString,timeToString).getData();
        if (!healthData.isEmpty()) {
            healthGroup.put("count", healthData.get("total"));
        }
        responseBody.add(healthGroup);

        // 回傳
        return BaseResponse.ok(responseBody);
    }

    @Override
    public BaseResponse getAgentConnectivityTotalAmount(Long eid, Date timeFrom, Date timeTo) {
        // 處理時間格式，轉為美國時區
        String timeFromString = "";
        String timeToString = "";
        if (Objects.nonNull(timeFrom)) {
            ZonedDateTime zonedDateTimeFrom = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timeFrom.getTime()), ZoneId.of("Etc/GMT"));
            timeFromString = zonedDateTimeFrom.format(FORMATTER);
        }
        if (Objects.nonNull(timeTo)) {
            ZonedDateTime zonedDateTimeTo = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timeTo.getTime()), ZoneId.of("Etc/GMT"));
            timeToString = zonedDateTimeTo.format(FORMATTER);
        }

        // 將siteIdsList轉為String
        List<String> siteIdsList = edrAgentMapper.getAgentSiteIdByEid(eid).stream()
                .map(map -> map.getOrDefault("siteId", "").toString())
                .collect(Collectors.toList());
        if (siteIdsList.isEmpty()) {
            return BaseResponse.ok();
        }
        String siteIds = String.join(",", siteIdsList);


        List<Map<String, Object>> responseBody = new ArrayList<>();
        // 解析數據 - 運行中
        Map<String, Object> naGroup = new HashMap<>();
        naGroup.put("title", "Not disabled");
        Map<String, Object> naData = (Map<String, Object>) edrv2Util.getNotDisabledTotalAmount(siteIds,timeFromString,timeToString).getData();
        if (!naData.isEmpty()) {
            naGroup.put("count", naData.get("total"));
        }
        responseBody.add(naGroup);

        // 解析數據 - 已關閉
        Map<String, Object> autoFullyDisabledGroup = new HashMap<>();
        autoFullyDisabledGroup.put("title", "Disabled");
        Map<String, Object> autoFullyDisabledData = (Map<String, Object>) edrv2Util.getDisabledTotalAmount(siteIds,timeFromString,timeToString).getData();
        if (!autoFullyDisabledData.isEmpty()) {
            autoFullyDisabledGroup.put("count", autoFullyDisabledData.get("total"));
        }
        responseBody.add(autoFullyDisabledGroup);

        // 解析數據 - 已關閉未重新啟動
        Map<String, Object> partiallyDisabledGroup = new HashMap<>();
        partiallyDisabledGroup.put("title", "Disabled and not rebooted");
        Map<String, Object> partiallyDisabledData = (Map<String, Object>) edrv2Util.getDisabledAndNotRebootedTotalAmount(siteIds,timeFromString,timeToString).getData();
        if (!partiallyDisabledData.isEmpty()) {
            partiallyDisabledGroup.put("count", partiallyDisabledData.get("total"));
        }
        responseBody.add(partiallyDisabledGroup);

        // 解析數據 - 代理錯誤
        Map<String, Object> errorGroup = new HashMap<>();
        errorGroup.put("title", "Agent error");
        Map<String, Object> errorData = (Map<String, Object>) edrv2Util.getAgentErrorTotalAmount(siteIds,timeFromString,timeToString).getData();
        if (errorData != null) {
            errorGroup.put("count", errorData.get("total"));
        }
        responseBody.add(errorGroup);

        // 回傳
        return BaseResponse.ok(responseBody);

    }

    @Override
    public BaseResponse checkAgentStatus(EdrAgentCheckStatusParam param) {
        // region 參數檢查
        Optional<BaseResponse> opt = checkParamIsEmpty(param, "params");
        if (opt.isPresent()) {
            return opt.get();
        }
        // endregion

        // 若planId不為空，需檢查並回傳排程下全部設備
        if (StringUtil.isNotEmpty(param.getPlanId())) {
            List<String> agentIds = edrScanPlanMapper.getAgentIdsByPlanId(param.getPlanId());
            param.setAgentIds(agentIds);
        }

        // 執行狀態檢查
        List<EdrAgentCheckStatus> edrAgentCheckStatusList = getEdrAgentCheckStatusList(param);

        return BaseResponse.ok(edrAgentCheckStatusList);
    }

    @Override
    public void exportAgentList(HttpServletResponse response, String eid, EdrAgentExportParam param) {
        // region 參數檢查
        List<String> params = Stream.of(
                        new AbstractMap.SimpleEntry<>(param.getAgentIds(), "agentIds"),
                        new AbstractMap.SimpleEntry<>(param.getColumns(), "columns")
                ).filter(entry -> checkParamIsEmpty(entry.getKey(), entry.getValue()).isPresent())
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(params)) {
            throw new RuntimeException("params " + String.join(",", params) + " is empty.");
        }
        // endregion

        // 取得Agent
        EdrAgentParam edrAgentParam = new EdrAgentParam(eid, param.getAgentIds());
        List<EdrAgentField> agentList = edrAgentMapper.getAgent(edrAgentParam);

        // 過濾掉已經處理過的，剩下的才進行 cron 計算
        agentList = getNextExecutionTime(agentList);

        List<Map<String, Object>> dataList = agentList.stream()
                .peek(agent -> {
                    // 處理組件狀態是否添加待移除
                    if (agent.getIsPendingUninstall()) {
                        String operationalState = agent.getOperationalState() + "-isPendingUninstall";
                        agent.setOperationalState(operationalState);
                    }
                })
                .map(EdrAgentField::toMap)
                .peek(agent -> {
                    // 若為空則表示未排程
                    if (Objects.isNull(agent.get("planStatus"))) {
                        agent.put("planStatus", "noPlan");
                    }
                })
                .collect(Collectors.toList());

        // 處理標題語系轉換
        String[] headMap = param.getColumns().stream()
                .map(column -> messageUtils.noExceptionLogGet(column, param.getLanguage()))
                .toArray(String[]::new);

        // 調整dataList中的值變更語系及轉換日期格式
        dataList = languageAndDateFormat(dataList, param.getLanguage());

        // 處理匯出
        NoModelWriteData noModelWriteData = new NoModelWriteData();
        noModelWriteData.setFileName("EDR2.0_Agent" + "_" +
                DateUtil.getSomeDateFormatString(LocalDateTime.now(), DateUtil.UNSIGNED_DATE_TIME_FORMATTER));
        noModelWriteData.setHeadMap(headMap);
        noModelWriteData.setDataStrMap(param.getColumns().toArray(new String[0]));
        noModelWriteData.setDataList(dataList);

        easyExcelUtil.noModelWrite(noModelWriteData, response);
    }

    @Override
    public List<EdrAgentCheckStatus> getEdrAgentCheckStatusList(EdrAgentCheckStatusParam param){
        // 取得Agent
        EdrAgentParam edrAgentParam = new EdrAgentParam(
                param.getSiteIds(),
                param.getAgentIds(),
                param.getLastSeenStart(),
                param.getLastSeenEnd(),
                IntegerUtil.isNotEmpty(param.getPageNumber()) ? (param.getPageNumber() - 1) * param.getItemsPerPage() : null,
                IntegerUtil.isNotEmpty(param.getItemsPerPage()) ? param.getItemsPerPage() : null
        );

        // 獲取 Agent 列表
        List<EdrAgentField> agentList = edrAgentMapper.getAgent(edrAgentParam);

        // 是否包含在排程計畫
        if (Objects.nonNull(param.getIncludeScanPlan())) {
            // 查詢排程計畫設備清單
            List<EdrScanPlanAgent> scanPlanAgentList = edrScanPlanMapper.getScanPlanAgents(null, null, false);
            List<String> saIdList = scanPlanAgentList.stream()
                    .map(EdrScanPlanAgent::getSaId)
                    .map(StringUtil::toString)
                    .collect(Collectors.toList());
            agentList = agentList.stream()
                    // true -> 查包含在saIdList的設備
                    // false -> 查不包含在saIdList的設備
                    .filter(agent -> Objects.equals(param.getIncludeScanPlan(), saIdList.contains(agent.getId())))
                    .collect(Collectors.toList());
        }

        // 執行檢查狀態
        return checkAgentStatus(agentList, param.getCheckPoint());
    }

    @Override
    public BaseResponse saveIsWarning(EdrAgentIsWarningParam param) {
        // region 參數檢查
        List<String> params = Stream.of(
                        new AbstractMap.SimpleEntry<>(param.getIsWarning(), "isWarning"),
                        new AbstractMap.SimpleEntry<>(param.getAgentIds(), "agentIds"),
                        new AbstractMap.SimpleEntry<>(param.getSiteId(), "siteId")
                ).filter(entry -> checkParamIsEmpty(entry.getKey(), entry.getValue()).isPresent())
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(params)) {
            return BaseResponse.error(ResponseCode.PARAM_IS_EMPTY, params);
        }
        // endregion

        // 更新MySQL數據
        Integer affectedRow = edrAgentMapper.updateAgentIsWarning(param);
        if (affectedRow <= 0) {
            return BaseResponse.error(ResponseCode.EDR_AGENT_SAVE_ISWARNING_ERROR);
        }

        // 更新SR數據
        StringBuilder sb = new StringBuilder();
        sb.append("UPDATE servicecloud.Sentinelone_Agents SET isWarningEnable = ").append(param.getIsWarning());
        sb.append(" WHERE siteId = '").append(param.getSiteId()).append("'");
        sb.append(" AND agentId IN ('").append(String.join("','", param.getAgentIds())).append("')");

        String sql = StringUtil.toString(sb);
        affectedRow = bigDataUtil.srSave(sql);
        if (affectedRow <= 0) {
            return BaseResponse.error(ResponseCode.EDR_AGENT_SAVE_ISWARNING_ERROR);
        }

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse selectIsWarning(String siteId, Boolean isWarning) {
        // 查詢設備是否預警
        List<EdrAgentIsWarning> agentFields = edrAgentMapper.selectAgentIsWarning(siteId, isWarning);

        return BaseResponse.ok(agentFields);
    }

    private List<Map<String, Object>> languageAndDateFormat(List<Map<String, Object>> dataList, String language) {
        return Observable.fromIterable(dataList)
                .map(data -> {
                    Observable.fromIterable(data.keySet())
                            .blockingForEach(key -> {
                                // 歷遍轉換語系
                                data.put(key, messageUtils.noExceptionLogGet(StringUtil.toString(data.get(key)), language));
                            });
                    return data;
                })
                .toList()
                .blockingGet();
    }

    private List<EdrAgentCheckStatus> checkAgentStatus(List<EdrAgentField> agentList, String checkPoint) {
        return Observable.fromIterable(agentList)
                .map(agent -> {
                    EdrAgentCheckStatus checkStatus = new EdrAgentCheckStatus();
                    checkStatus.setId(agent.getId());
                    checkStatus.setEid(agent.getEid());
                    checkStatus.setSiteId(agent.getSiteId());
                    checkStatus.setAgentId(agent.getAgentId());
                    checkStatus.setAgentUUId(agent.getAgentUUId());
                    checkStatus.setOs(agent.getOs());
                    checkStatus.setGroupTitle(agent.getGroupTitle());
                    checkStatus.setSiteName(agent.getSiteName());
                    checkStatus.setEndPointName(agent.getEndpointName());
                    checkStatus.setLastReportedIP(agent.getLastReportedIP());
                    checkStatus.setCheckStatus(checkStatus(agent, checkPoint));
                    checkStatus.setLastSuccessfulScanDate(agent.getLastSuccessfulScanDate());
                    return checkStatus;
                })
                .toList()
                .blockingGet();
    }

    private String checkStatus(EdrAgentField agent, String checkPoint) {
        // 依照優先顯示順序判斷 findFisrt 確保前面條件優先回傳原狀態值，都沒有錯誤則回傳OK
        return Stream.of(
                        new AbstractMap.SimpleEntry<>(
                                SCAN_ON.equalsIgnoreCase(checkPoint) && "In progress".equalsIgnoreCase(agent.getScanStatus()),
                                agent.getScanStatus()
                        ),
                        new AbstractMap.SimpleEntry<>(
                                SCAN_OFF.equalsIgnoreCase(checkPoint) && !"In progress".equalsIgnoreCase(agent.getScanStatus()),
                                agent.getScanStatus()
                        ),
                        new AbstractMap.SimpleEntry<>(
                                NETWORK_DISCONNECT.equalsIgnoreCase(checkPoint) && !"Connected".equalsIgnoreCase(agent.getNetworkStatus()),
                                agent.getNetworkStatus()
                        ),
                        new AbstractMap.SimpleEntry<>(
                                NETWORK_CONNECT.equalsIgnoreCase(checkPoint) && "Connected".equalsIgnoreCase(agent.getNetworkStatus()),
                                agent.getNetworkStatus()
                        ),
                        new AbstractMap.SimpleEntry<>(
                                UNINSTALL.equalsIgnoreCase(checkPoint) &&
                                        !NOT_DISABLED.equalsIgnoreCase(agent.getOperationalState()) &&
                                        Boolean.FALSE.equals(agent.getIsPendingUninstall()),
                                agent.getOperationalState()
                        ),
                        new AbstractMap.SimpleEntry<>(!UNINSTALL.equalsIgnoreCase(checkPoint) &&
                                !NOT_DISABLED.equalsIgnoreCase(agent.getOperationalState()),
                                agent.getOperationalState()
                        ),
                        new AbstractMap.SimpleEntry<>(
                                !ONLINE.equalsIgnoreCase(agent.getManagmentConnectivity()),
                                agent.getManagmentConnectivity()
                        )
                )
                .filter(AbstractMap.SimpleEntry::getKey)
                .map(AbstractMap.SimpleEntry::getValue)
                .findFirst()
                .orElse("OK");
    }

    public BaseResponse networkIsolationOrConnect(EdrNetworkIsolationParam param) {
        //有未傳的參數
        List<String> emptyParam = Stream.of(
                new AbstractMap.SimpleEntry<>("siteIds", param.getSiteIds()),
                new AbstractMap.SimpleEntry<>("agentIds", param.getAgentIds()),
                new AbstractMap.SimpleEntry<>("executeAction", param.getExecuteAction())
                ).filter(entry -> checkParamIsEmpty(entry.getValue(), entry.getKey()).isPresent())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if(!emptyParam.isEmpty()) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, emptyParam.stream().collect(Collectors.joining(",")));
        }

        //參數siteIds陣列與agentIds陣列長度不相同
        if(param.getSiteIds().size() != param.getAgentIds().size()) {
            return BaseResponse.error(ResponseCode.INVALID_PARAM);
        }
        //組原廠API之RequestBody
        Map<String, Object> request = new HashMap<>();
        request.put("data",new HashMap<>());

        Map<String, Object> filterMap = new HashMap<>();
        filterMap.put("siteIds", param.getSiteIds());
        filterMap.put("ids", param.getAgentIds());
        request.put("filter", filterMap);

        Map<String, Object> res = new HashMap<>();
        if("connect".equals(param.getExecuteAction())) {
            res = edrv2Util.sendRequest(NETWORK_CONNECT_URI, HttpMethod.POST, request);
        }
        else if("disconnect".equals(param.getExecuteAction())) {
            res = edrv2Util.sendRequest(NETWORK_DISCONNECT_URI, HttpMethod.POST, request);
        }

        if(Objects.nonNull(res.getOrDefault("error",null))) {
            Map<String, Object> map = GsonUtil.getInstance().fromJson(StringUtil.toString(res.get("error")), Map.class);
            //取原廠API回傳之錯誤訊息的title
            List<String> titles = ((List<Map<String, Object>>) map.get("errors")).stream()
                    .map(error -> (String) error.get("title")) // 提取 title
                    .collect(Collectors.toList());
            return BaseResponse.error("-1",titles.stream().collect(Collectors.joining(",")));
        }

        if(res.getOrDefault("affected",0).equals(0)) {
            return BaseResponse.error(ResponseCode.EDR_AGENT_FOUND_ZERO);
        }
        if(Objects.nonNull(res.getOrDefault("affected",null))) {
            List<Map<String, String>> data = new ArrayList<>();
            for (int i = 0; i < param.getSiteIds().size(); i++) {
                Map<String, String> map1 = new HashMap<>();
                map1.put("siteId", param.getSiteIds().get(i));
                map1.put("agentId", param.getAgentIds().get(i));
                data.add(map1);
            }
            String status = "connect".equals(param.getExecuteAction()) ? "Connected" : "Disconnected";
            //手動更改MySQL中的狀態
            edrAgentMapper.updateAgentNetworkStatus(data, status);
        }

        return BaseResponse.ok();
    }

    private void scanStatusExecRecord(List<EdrAgentField> agentList, List<EdrAgentField> edrAgentFieldRequest) {
        // 取得排程任務內的設備清單
        List<EdrScanPlanAgent> edrScanPlanAgentList = edrScanPlanMapper.getScanPlanAgents(null, null, false);
        List<Long> scanPlanSaIdList = edrScanPlanAgentList.stream().map(EdrScanPlanAgent::getSaId).collect(Collectors.toList());

        // 篩選包含在排程任務內，且是由 IN_PROGRESS 轉為其他狀態的設備
        List<EdrAgentField> scanStatusAgentList = agentList.stream()
                .filter(agent -> scanPlanSaIdList.contains(LongUtil.objectToLong(agent.getId())))
                .flatMap(agent -> edrAgentFieldRequest.stream()
                        .filter(requestAgent -> agent.getAgentId().equals(requestAgent.getAgentId()))
                        .filter(requestAgent -> IN_PROGRESS.equalsIgnoreCase(agent.getScanStatus()) && !IN_PROGRESS.equalsIgnoreCase(requestAgent.getScanStatus()))
                        .peek(requestAgent -> requestAgent.setId(agent.getId()))
                ).collect(Collectors.toList());

        // 需要更新排程執行狀況的設備Id
        List<Long> saIdList = scanStatusAgentList.stream().map(agent -> LongUtil.objectToLong(agent.getId())).collect(Collectors.toList());

        // 排程計劃需要更新的設備列表
        List<EdrScanPlanAgent> updateEdrScanPlanAgentList = edrScanPlanAgentList.stream()
                .filter(edrScanPlanAgent -> saIdList.contains(edrScanPlanAgent.getSaId()))
                .collect(Collectors.toList());

        // 寫入紀錄
        updateEdrScanPlanAgentList.forEach(edrScanPlanAgent ->
                scanStatusAgentList.stream()
                        .filter(agent -> LongUtil.objectToLong(agent.getId()).equals(edrScanPlanAgent.getSaId()))
                        .findFirst()
                        .ifPresent(agent -> {
                            EdrScanPlanAgentActivity scanPlanAgentActivity = edrScanPlanMapper.getStartAgentActivity(edrScanPlanAgent.getId());

                            // 若查無設備活動紀錄開始時間 或是 設備在該任務活動紀錄 已有兩筆以上則跳過(開始及結束紀錄)
                            if (Objects.isNull(scanPlanAgentActivity) || scanPlanAgentActivity.getActivityCount() >= 2) {
                                return;
                            }

                            // 檢查資料時間時否大於開始時間
                            LocalDateTime lastSuccessfulScanDate = DateUtil.parseToLocalDateTime(agent.getLastSuccessfulScanDate());
                            if (lastSuccessfulScanDate.isBefore(scanPlanAgentActivity.getCreatedAt())) {
                                return;
                            }

                            Long sstaId = scanPlanAgentActivity.getSstaId();
                            String scanStatus = agent.getScanStatus().toUpperCase();
                            String memo = MEMO_COMPLETED;
                            if (FAILED.equalsIgnoreCase(scanStatus)) {
                                memo = MEMO_FAILED;
                            }
                            if (ABORTED.equalsIgnoreCase(scanStatus)) {
                                memo = MEMO_ABORTED;
                            }

                            edrScanPlanService.execRecord(Collections.singletonList(edrScanPlanAgent.getId()), scanStatus, memo, sstaId);
                        })
        );
    }

    private void operationalStateEnableRecord(List<EdrAgentField> agentList, List<EdrAgentField> edrAgentFieldRequest) {
        // 篩選出自動啟用的設備
        List<EdrAgentField> operationalStateAgentList = agentList.stream()
                .flatMap(agent -> edrAgentFieldRequest.stream()
                        .filter(requestAgent -> agent.getAgentId().equals(requestAgent.getAgentId()))
                        .filter(requestAgent -> !NOT_DISABLED.equalsIgnoreCase(agent.getOperationalState()) && NOT_DISABLED.equalsIgnoreCase(requestAgent.getOperationalState()))
                        .peek(requestAgent -> requestAgent.setId(agent.getId()))
                ).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(operationalStateAgentList)) {
            return;
        }

        // 處理操作紀錄
        List<EdrOrgCollectorProcessRecordSaveDTO> records = operationalStateAgentList.stream()
                .map(agent -> {
                    EdrOrgCollectorProcessRecordSaveDTO edrOrgCollectorProcessRecordSaveDTO = new EdrOrgCollectorProcessRecordSaveDTO();
                    edrOrgCollectorProcessRecordSaveDTO.setEid(LongUtil.objectToLong(agent.getEid()));
                    edrOrgCollectorProcessRecordSaveDTO.setServerId(SERVERID);
                    edrOrgCollectorProcessRecordSaveDTO.setOrgId(LongUtil.objectToLong(agent.getSiteId()));
                    edrOrgCollectorProcessRecordSaveDTO.setCollectorId(LongUtil.objectToLong(agent.getAgentId()));
                    edrOrgCollectorProcessRecordSaveDTO.setUserName("系統");
                    edrOrgCollectorProcessRecordSaveDTO.setEnable(1);
                    return edrOrgCollectorProcessRecordSaveDTO;
                }).collect(Collectors.toList());

        // 批量保存操作紀錄
        edrReportService.saveProcessRecords(records);
    }

    private List<EdrAgentField> getNextExecutionTime(List<EdrAgentField> agentList) {
        agentList.stream()
                .filter(agent -> Boolean.TRUE.equals(agent.getPlanStatus())
                        && !N_A.equals(agent.getCrons())
                        && agent.getCrons() != null)
                .forEach(agent -> {
                    try {
                        // 判斷 crons 表達式類型 (6 個字段代表 UNIX，7 個字段代表 QUARTZ)
                        String[] cronFields = agent.getCrons().trim().split("\\s+");
                        CronType cronType = cronFields.length == 7 ? CronType.QUARTZ : CronType.UNIX;
                        CronParser parser = new CronParser(CronDefinitionBuilder.instanceDefinitionFor(cronType));
                        Cron cron = parser.parse(agent.getCrons());
                        cron.validate();
                        ExecutionTime executionTime = ExecutionTime.forCron(cron);

                        // 如果上次執行時間為unll，則抓取當下的時間做計算
                        ZonedDateTime baseTime;
                        if (agent.getLastExecutionTime() != null) {
                            // 有歷史執行時間就用它
                            baseTime = agent.getLastExecutionTime().toInstant().atZone(ZoneId.systemDefault());
                        } else if (Boolean.TRUE.equals(agent.getPlanStatus())) {
                            // 沒有歷史執行時間，且掃描計畫狀態=True，就用現在時間來算
                            baseTime = ZonedDateTime.now();
                        } else {
                            agent.setNextExcuteTime(null);
                            return;
                        }
                        // 計算下一次執行時間
                        Optional<ZonedDateTime> nextRun = executionTime.nextExecution(baseTime);
                        agent.setNextExcuteTime(nextRun.map(zdt -> Date.from(zdt.toInstant())).orElse(null));
                    } catch (Exception e) {
                        log.error("Error parsing crons for agentId: {}, crons: {}", agent.getAgentId(), agent.getCrons(), e);
                        agent.setNextExcuteTime(null);
                    }
                });

        return agentList;
    }

    private void removeSRAgent(Long siteId, List<String> agentIdList) {
        StringBuilder sb = new StringBuilder();
        sb.append("DELETE FROM servicecloud.Sentinelone_Agents");
        sb.append(" WHERE siteId = '").append(siteId).append("'");
        sb.append(" AND agentId IN ('").append(String.join("','", agentIdList)).append("')");

        String sql = StringUtil.toString(sb);
        Integer affectedRow = bigDataUtil.srSave(sql);
        if (affectedRow <= 0) {
            log.error("Error while remove SR sentinelone_agent, siteId: {}, agentIdList: {}", siteId, agentIdList);
        }
    }
}
