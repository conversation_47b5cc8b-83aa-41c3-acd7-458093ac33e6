package com.digiwin.escloud.aiobasic.edrv2.service.impl;

import com.alibaba.excel.EasyExcel;
import com.digiwin.escloud.aiobasic.edr.dao.EdrKbMapper;
import com.digiwin.escloud.aiobasic.edrv2.constant.Edrv2Const;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrAgentMapper;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrApplicationMapper;
import com.digiwin.escloud.aiobasic.edrv2.dao.EdrCustomerV2Mapper;
import com.digiwin.escloud.aiobasic.edrv2.model.*;
import com.digiwin.escloud.aiobasic.edrv2.service.IEdrCustomerV2Service;
import com.digiwin.escloud.aiobasic.mail.service.VoidOrgNotifyMailService;
import com.digiwin.escloud.aiobasic.util.BigDataUtil;
import com.digiwin.escloud.aiobasic.util.EasyExcelUtil;
import com.digiwin.escloud.aiobasic.util.ExcelStyleHandler;
import com.digiwin.escloud.aioitms.model.bigdata.UploadBigDataContext;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.model.JdbcSqlInfo;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;
import com.digiwin.escloud.common.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class EdrCustomerV2Service implements IEdrCustomerV2Service, ParamCheckHelp {
    @Autowired
    BigDataUtil bigDataUtil;
    @Autowired
    EasyExcelUtil easyExcelUtil;
    @Autowired
    EdrCustomerV2Mapper edrCustomerV2Mapper;
    @Autowired
    EdrAgentMapper edrAgentMapper;
    @Autowired
    EdrApplicationMapper edrApplicationMapper;
    @Autowired
    RestTemplate restTemplate;
    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Autowired
    private EdrKbMapper edrKbMapper;
    @Autowired
    private VoidOrgNotifyMailService voidOrgNotifyMailService;
    @Value("${ENVIRONMENT:dev}")
    private String environment;
    private static final String serverId = "365750770160999"; // EDRv2專用
    private static final String RELEASE = "release";
    private static final String NONRELEASE = "nonRelease";

    @Override
    public BaseResponse saveCustomerOrg(EdrCustomerOrg edrCustomerOrgRequest) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(edrCustomerOrgRequest.getId(), "id");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        // 檢查組織是否已存在
        EdrCustomerOrg edrCustomerOrg = edrCustomerV2Mapper.selectCustomerOrg(edrCustomerOrgRequest.getId());
        Boolean checkOrgExist = Objects.nonNull(edrCustomerOrg);

        Integer checkAffectRow = 0;
        if (checkOrgExist) {
            // 若已有租戶綁定則不予更新
            if (Objects.nonNull(edrCustomerOrg.getEid()) && edrCustomerOrgRequest.getCheckEid()) {
                Map<String, Object> data = new HashMap<>();
                data.put("serviceCode", edrCustomerOrg.getServiceCode());
                data.put("customerName", edrCustomerOrg.getCustomerName());
                return BaseResponse.error(ResponseCode.EDR_UPDATE_ORG_FAILED_EID_ALREADY_IN_USE, data);
            }
            // 若已手動作廢則不同步數據
            if ("void".equals(edrCustomerOrg.getState()) && !edrCustomerOrgRequest.getCheckEid()) {
                return BaseResponse.error(ResponseCode.EDR_UPDATE_FAILED_ORG_IS_DEPRECATED);
            }
            //原廠作廢而資料庫還未作廢
            if(!"expired".equals(edrCustomerOrg.getState()) && "expired".equals(edrCustomerOrgRequest.getState())) {
                voidOrgNotifyMailService.sendMail(edrCustomerOrg.getServiceCode(), "S1EDR" , edrCustomerOrg.getName());
            }

            // 若有帶originId表示變更綁定組織，需先解除原組織綁定
            if (Objects.nonNull(edrCustomerOrgRequest.getOriginId())) {
                edrCustomerV2Mapper.deleteCustomerOrg(edrCustomerOrgRequest.getOriginId());
            }

            // 執行更新數據
            edrCustomerOrgRequest.setUpdatedAt(new Date());
            checkAffectRow = edrCustomerV2Mapper.updateCustomerOrg(edrCustomerOrgRequest);
            if (checkAffectRow.equals(0)) {
                return BaseResponse.error(ResponseCode.EDR_UPDATE_ORG_FAILED);
            }

            // 若參數有帶eid則為綁定組織，則需執行同步事件
            if (Objects.nonNull(edrCustomerOrgRequest.getEid())) {
                // 更新組織後執行同步事件
                syncThreats(edrCustomerOrgRequest.getId());

                // 更新組織後執行同步應用軟體
                syncApplication(edrCustomerOrgRequest.getId());

                // 更新組織後執行同步設備
                syncAgent(edrCustomerOrgRequest.getId());
            }
        } else {
            // 執行新增數據
            checkAffectRow = edrCustomerV2Mapper.createCustomerOrg(edrCustomerOrgRequest);
            if (checkAffectRow.equals(0)) {
                return BaseResponse.error(ResponseCode.INSERT_FAILD);
            }

            // 更新組織後執行同步事件
            syncThreats(edrCustomerOrgRequest.getId());

            // 更新組織後執行同步應用軟體
            syncApplication(edrCustomerOrgRequest.getId());

            // 更新組織後執行同步設備
            syncAgent(edrCustomerOrgRequest.getId());
        }
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse deprecateCustomerOrg(EdrCustomerOrgDeprecateRequest edrCustomerOrgDeprecateRequest) {
        // 判定參數是否有內容
        List<String> stringParams = new ArrayList<>();
        if (!Optional.ofNullable(edrCustomerOrgDeprecateRequest.getId()).isPresent()) {
            stringParams.add("id");
        }
        if (!Optional.ofNullable(edrCustomerOrgDeprecateRequest.getState()).isPresent() ||
                edrCustomerOrgDeprecateRequest.getState().isEmpty()) {
            stringParams.add("state");
        }
        if (!Optional.ofNullable(edrCustomerOrgDeprecateRequest.getProcessUserName()).isPresent() ||
                edrCustomerOrgDeprecateRequest.getProcessUserName().isEmpty()) {
            stringParams.add("processUserName");
        }
        if (!Optional.ofNullable(edrCustomerOrgDeprecateRequest.getVoidReason()).isPresent() ||
                edrCustomerOrgDeprecateRequest.getVoidReason().isEmpty()) {
            stringParams.add("voidReason");
        }
        if (stringParams.stream().count() > 0) {
            return BaseResponse.dynamicError(
                    ResponseCode.PARAM_IS_EMPTY,
                    stringParams.stream().collect(Collectors.joining(",")));
        }

        // 判定組織是否作廢
        if (edrCustomerOrgDeprecateRequest.getState().equals("N")) {
            // 以訂閱到期判定狀態單字
            Date expiration = edrCustomerV2Mapper.selectCustomerOrg(edrCustomerOrgDeprecateRequest.getId()).getExpiration();
            Long now = new Date().getTime();
            if (expiration != null && (expiration.getTime() < now)) {
                edrCustomerOrgDeprecateRequest.setState("expired");
                //TODO call 作廢寄信Service 手動，不一定
            } else {
                edrCustomerOrgDeprecateRequest.setState("active");
            }
        } else if (edrCustomerOrgDeprecateRequest.getState().equals("Y")) {
            edrCustomerOrgDeprecateRequest.setState("void");
        }

        Integer checkAffectRow = 0;
        checkAffectRow = edrCustomerV2Mapper.deprecateCustomerOrg(edrCustomerOrgDeprecateRequest);
        if (checkAffectRow.equals(0)) {
            return BaseResponse.error(ResponseCode.UPDATE_FAILD);
        }

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse deleteCustomerOrg(Long id) {
        if (!Optional.ofNullable(id).isPresent()) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY);
        }

        Integer checkAffectRow = 0;
        checkAffectRow = edrCustomerV2Mapper.deleteCustomerOrg(id);
        if (checkAffectRow.equals(0)) {
            return BaseResponse.error(ResponseCode.UPDATE_FAILD);
        }

        // 組織相關事件刪除
        if (!deleteThreads(id)) {
            return BaseResponse.error(ResponseCode.REMOVE_FAILD);
        }

        // 組織相關應用軟體刪除
        if (!(edrApplicationMapper.removeApplication(id, null) > 0)) {
            log.error("EDRv2 Remove Application Error");
        }

        // 組織相關設備刪除
        if (!(edrAgentMapper.removeAgent(id, null, null) > 0)) {
            log.error("EDRv2 Remove Agent Error");
        }

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse getCustomerOrg(Long eid, String state, Boolean eidCheck, Integer pageNum, Integer pageSize) {

        if (!IntegerUtil.isEmpty(pageNum) && !IntegerUtil.isEmpty(pageSize)) {
            PageHelper.startPage(pageNum, pageSize);
        }
        List<EdrCustomerOrgSite> dataList = edrCustomerV2Mapper.selectCustomerOrgSite(eid, state, eidCheck);

        return BaseResponse.ok(new PageInfo<>(dataList));
    }

    @Override
    public BaseResponse getCustomerOrgEid(List<Long> idList) {

        List<EdrCustomerOrgEid> dataList = edrCustomerV2Mapper.selectCustomerOrgEid(idList);

        return BaseResponse.ok(dataList);
    }

    @Override
    public BaseResponse saveThreats(List<EdrCustomerThreat> edrCustomerThreatList) {

        if (CollectionUtils.isEmpty(edrCustomerThreatList)) {

            // 若傳入數據為空，則查詢全部有綁定的組織eid做時間更新
            List<EdrCustomerOrgSite> dataList = edrCustomerV2Mapper.selectCustomerOrgSite(null, "active", true);
            dataList.forEach(edrCustomerOrgSite -> {
                // 更新同步時間
                stringRedisTemplate.opsForValue().set("Threats_Sync_Time_" + edrCustomerOrgSite.getEid(), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            });

            return BaseResponse.error(ResponseCode.EDR_THREAT_LIST_IS_EMPTY);
        }

        List<Map<String, Object>> threatList = new ArrayList<>();
        edrCustomerThreatList.forEach(threat -> {
            // 更新同步時間
            stringRedisTemplate.opsForValue().set("Threats_Sync_Time_" + StringUtil.toString(threat.getEid()), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            Map<String, Object> threatMap = convertToMap(threat);
            threatMap.put("createdAt", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(threat.getCreatedAt()));
            threatMap.put("updatedAt", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(threat.getUpdatedAt()));
            threatMap.put("identifiedAt", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(threat.getIdentifiedAt()));
            threatMap.put("sentinelone_classification", threat.getClassification());

            UploadBigDataContext uploadBigDataContext = new UploadBigDataContext(threat.getEid(), "", null, null, "SentinelOne_Threats", false, threatMap);
            threatList.addAll(bigDataUtil.createParentUploadStruct(uploadBigDataContext));
        });

        if (!bigDataUtil.simulateLocalUploadData(threatList)) {
            return BaseResponse.error(ResponseCode.EDR_SAVE_THREATS_FAILED);
        }
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse getThreatList(EdrCustomerThreatsParam edrCustomerThreatsParam, String area) {
        //region 參數檢查
        List<String> params = new ArrayList<>();

        if (IntegerUtil.isEmpty(edrCustomerThreatsParam.getPageSize())) {
            params.add("pageSize");
        }
        if (IntegerUtil.isEmpty(edrCustomerThreatsParam.getPageNum())) {
            params.add("pageNum");
        }
        if (!params.isEmpty()) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, params.stream().collect(Collectors.joining(", ")));
        }
        // endregion

        // 取得事件數據
        List<Map<String, Object>> dataList = getThreats(edrCustomerThreatsParam, area);

        // id轉為字串回傳，處理id長度過長前端遺失精度問題
        dataList = dataList.stream()
                .map(data -> {
                    Object idObject = data.get("collectionId");
                    if (idObject instanceof Long) {
                        data.put("collectionId", StringUtil.toString(idObject));
                    }
                    return data;
                })
                .collect(Collectors.toList());

        // 取得事件數量
        Integer total = getThreatsCount(edrCustomerThreatsParam);
        Map<String, Object> res = new HashMap<>();
        res.put("total", total);
        res.put("pages", IntegerUtil.objectToInteger(Math.ceil((double) total / edrCustomerThreatsParam.getPageSize())));
        res.put("list", dataList);
        res.put("pageNum", edrCustomerThreatsParam.getPageNum());
        res.put("pageSize", edrCustomerThreatsParam.getPageSize());
        res.put("updateTime", stringRedisTemplate.opsForValue().get("Threats_Sync_Time_" + StringUtil.toString(edrCustomerThreatsParam.getEid())));

        return BaseResponse.ok(res);
    }

    @Override
    public BaseResponse getThreatDetailList(EdrCustomerThreatsParam edrCustomerThreatsParam, String area) {
        // region 參數檢查
        List<String> params = new ArrayList<>();
//        if (LongUtil.isEmpty(edrCustomerThreatsParam.getEid())) {
//            params.add("eid");
//        }
        if (StringUtils.isEmpty(edrCustomerThreatsParam.getCollectionId())) {
            params.add("collectionId");
        }
        if (StringUtils.isEmpty(edrCustomerThreatsParam.getThreatName())) {
            params.add("threatName");
        }
        if (IntegerUtil.isEmpty(edrCustomerThreatsParam.getPageSize())) {
            params.add("pageSize");
        }
        if (IntegerUtil.isEmpty(edrCustomerThreatsParam.getPageNum())) {
            params.add("pageNum");
        }
        if (!params.isEmpty()) {
            return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, params.stream().collect(Collectors.joining(", ")));
        }
        // endregion

        // 取得事件數據
        List<Map<String, Object>> dataList = getThreatsDetail(edrCustomerThreatsParam, area);

        // id轉為字串回傳，處理id長度過長前端遺失精度問題
        dataList = dataList.stream()
                .map(data -> {
                    Object idObject = data.get("id");
                    if (idObject instanceof Long) {
                        data.put("id", StringUtil.toString(idObject));
                    }

                    return data;
                })
                .collect(Collectors.toList());

        // 取得事件數量
        Integer total = getThreatsDetailCount(edrCustomerThreatsParam);

        Map<String, Object> res = new HashMap<>();
        res.put("total", total);
        res.put("pages", IntegerUtil.objectToInteger(Math.ceil((double) total / edrCustomerThreatsParam.getPageSize())));
        res.put("list", dataList);
        res.put("pageNum", edrCustomerThreatsParam.getPageNum());
        res.put("pageSize", edrCustomerThreatsParam.getPageSize());

        return BaseResponse.ok(res);
    }

    @Override
    public void getexportEventList(EdrCustomerThreatsParam params, HttpServletResponse response) {

        // 設定 HttpServletResponse
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=report.xlsx");

        try (ServletOutputStream outputStream = response.getOutputStream()) {
            // 取得主&子事件數據
            List<Map<String, Object>> maindataList = new ArrayList<>(); // 主事件

            // 查詢主事件
            for (Integer count = params.getPageNum(); count <= params.getPageEndNum(); count++) {
                params.setPageNum(count);
                maindataList.addAll(getThreats(params, params.getArea()));
            }

            // 取子事件
            params.setPageNum(1);
            ExecutorService executorService = Executors.newFixedThreadPool(5);

            // 初始化 Excel 匯出數據
            List<EdrCustomerThreatExportExcel> exportList =new ArrayList<>();

            // 迭代處理主事件並篩選對應的子事件
            maindataList.forEach(item -> {
                executorService.submit(()-> {
                    try{
                        // 為每個執行緒創建 params 的副本
                        EdrCustomerThreatsParam threadParams = new EdrCustomerThreatsParam();
                        threadParams.setCollectionId(Objects.toString(item.get("collectionId"), ""));
                        threadParams.setThreatName(Objects.toString(item.get("threatName"), ""));
                        threadParams.setPageSize(getThreatsDetailCount(threadParams));
                        threadParams.setSiteIds(Objects.toString(item.get("siteId"), ""));
                        threadParams.setArea(params.getArea()); // 設定其他必要參數

                        //List<Map<String, Object>> detailData = getThreatsDetail(params, params.getArea()); // 獲取子事件
                        List<Map<String, Object>> detailData = getThreatsDetail(threadParams, threadParams.getArea()); // 獲取子事件

                        detailData.forEach(detail -> {
                            // 將數據轉換為 EdrCustomerThreatExportExcel 格式
                            EdrCustomerThreatExportExcel exportExcel = new EdrCustomerThreatExportExcel();
                            exportExcel.setThreatName(Objects.toString(item.get("threatName"), ""));
                            exportExcel.setThreatCount(IntegerUtil.objectToInteger(item.get("threatCount"), 0));
                            exportExcel.setClassification(Objects.toString(item.get("classification"), ""));
                            exportExcel.setRecentTime(Objects.toString(item.get("recentTime"), ""));
                            exportExcel.setProcessDescription(Objects.toString(item.get("processDescription"), ""));
                            exportExcel.setId(Objects.toString(detail.get("id"), ""));
                            exportExcel.setFilePath(Objects.toString(detail.get("filePath"), ""));
                            exportExcel.setAgentComputerName(Objects.toString(detail.get("agentComputerName"), ""));
                            exportExcel.setClassificationSource(Objects.toString(detail.get("classificationSource"), ""));
                            exportExcel.setCreatedAt(Objects.toString(detail.get("createdAt"), ""));
                            exportExcel.setUpdatedAt(Objects.toString(detail.get("updatedAt"), ""));
                            exportExcel.setEventDescription(Objects.toString(detail.get("eventDescription"), ""));

                            // 設置 eventStatus 事件狀態
                            String eventStatus = Objects.toString(detail.get("eventStatus"), "");
                            if ("CN".equals(params.getArea())) {
                                switch (eventStatus) {
                                    case "solved":
                                        exportExcel.setEventStatus("已解决");
                                        break;
                                    case "createIssue":
                                        exportExcel.setEventStatus("已立案");
                                        break;
                                    case "notProcessedYet":
                                        exportExcel.setEventStatus("暂不处理");
                                        break;
                                    default:
                                        exportExcel.setEventStatus("-");
                                        break;
                                }

                            } else if ("TW".equals(params.getArea())) {
                                switch (eventStatus) {
                                    case "solved":
                                        exportExcel.setEventStatus("已解決");
                                        break;
                                    case "createIssue":
                                        exportExcel.setEventStatus("已立案");
                                        break;
                                    case "notProcessedYet":
                                        exportExcel.setEventStatus("暫不處理");
                                        break;
                                    default:
                                        exportExcel.setEventStatus("－");
                                        break;
                                }
                            }

                            // 設置 releaseRemark 放行狀態
                            String releaseRemark = Objects.toString(detail.get("releaseStatus"), "");
                            if ("release".equals(releaseRemark)) {
                                exportExcel.setReleaseStatus("已放行");
                            }

                            exportExcel.setIssueCode(Objects.toString(detail.get("issueCode"), ""));
                            exportList.add(exportExcel);
                        });
                    } catch (Exception e) {
                        log.error("Executors getexportEventList error", e);
                    }
                });
            });

            // 等待所有任務完成
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (Exception e) { //20241119:代码稽核 InterruptedException e 改为 Exception e
                executorService.shutdownNow();
            }

            // 根據區域動態設置 Excel 標頭 (簡體或繁體)
            List<List<String>> headers = getHeadersBasedOnArea(params.getArea());

            // 將數據寫入到 Excel
            EasyExcel.write(outputStream, EdrCustomerThreatExportExcel.class)
                    .registerWriteHandler(new ExcelStyleHandler())
                    .inMemory(true)
                    .head(headers)
                    .sheet("事件列表")
                    .doWrite(exportList);

        } catch (Exception e) {
            // 記錄錯誤，不做重定向或嘗試寫入 response
            log.error("EDRv2 Get exporting event list Failed: ", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public BaseResponse syncEvent(Map<String, Object> param) {
        try {
            String url = Edrv2Const.SYNC_THREATS_URI;

            // 建立Body數據
            Map<String, Object> requestBody = new HashMap<>();
            Long eid = LongUtil.objectToLong(param.get("eid"));
            List<String> siteIdList = edrCustomerV2Mapper.getIdByEid(eid);
            requestBody.put("siteIds", siteIdList);
            requestBody.put("env", environment);
            requestBody.put("syncNow", "Y");

            // 将 Map 转换为 JSON 字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonRequestBody = objectMapper.writeValueAsString(requestBody);

            // http請求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(jsonRequestBody, headers);
            CompletableFuture.runAsync(() -> restTemplate.postForEntity(url, request, String.class));
            stringRedisTemplate.opsForValue().set("Threats_Sync_Time_" + StringUtil.toString(eid), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("EDRv2 Sync event Failed: ", e);
            return BaseResponse.dynamicError(ResponseCode.EDR_SYNC_EVENT_FAILED, e);
        }
    }

    @Override
    public BaseResponse getCustomerOrgAuthorizationInfo(Long eid) {
        return BaseResponse.ok(edrCustomerV2Mapper.selectCustomerOrgAuthorizationInfo(eid));
    }

    public List<Map<String, Object>> getThreats(EdrCustomerThreatsParam edrCustomerThreatsParam, String area) {
        // 獲取組織列表
        List<Map<String, Object>> customerOrgList = edrCustomerV2Mapper.getCustomerOrgByServiceCode(edrCustomerThreatsParam.getServiceCode(), edrCustomerThreatsParam.getCustomerFullName(), edrCustomerThreatsParam.getSiteNames());

        // 因名稱可能相同而英文大小寫不同，故皆轉為大寫顯示
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT st.siteId, COALESCE(st.serviceCode, '') AS serviceCode, st.collectionId, ");
        stringBuilder.append("UPPER(st.threatName) AS threatName, COUNT(st.threatName) threatCount, st.classification, ");
        stringBuilder.append("MAX(IFNULL(st.lastUpdate, st.updatedAt)) AS recentTime ");
        stringBuilder.append("FROM servicecloud.SentinelOne_Threats st ");

        // 篩選條件
        buildWhereSql(stringBuilder, edrCustomerThreatsParam);
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getEventStatus()) ||
                ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getIssueCode())) {

            EdrEvent edrEventRequest = new EdrEvent();
            edrEventRequest.setServerId(serverId);
            edrEventRequest.setEventStatus(Objects.toString(edrCustomerThreatsParam.getEventStatus(), ""));
            edrEventRequest.setIssueCode(Objects.toString(edrCustomerThreatsParam.getIssueCode(), ""));
            List<Map<String, Object>> dataList = edrCustomerV2Mapper.getEdrEventList(edrEventRequest);
            List<String> eventIdList = dataList.stream().map(data -> data.get("eventId").toString()).collect(Collectors.toList());
            String eventIds = eventIdList.stream().map(String::valueOf).collect(Collectors.joining(","));

            if (ObjectUtils.isEmpty(edrCustomerThreatsParam.getIssueCode())
                    && Objects.equals(Objects.toString(edrCustomerThreatsParam.getEventStatus(), ""), "unsolved")
                    && StringUtils.isNotEmpty(eventIds)) {
                stringBuilder.append("AND st.id NOT IN (").append(eventIds).append(") ");
            } else {
                if (StringUtils.isEmpty(eventIds)) {
                    return new ArrayList<>();
                }
                stringBuilder.append("AND st.id IN (").append(eventIds).append(") ");
            }
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getSiteNames()) ||
                ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getServiceCode()) ||
                ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getCustomerFullName())) {
            String siteIds = customerOrgList.stream().map(data -> Objects.toString(data.get("siteId"), "")).collect(Collectors.joining(","));
            if (StringUtils.isEmpty(siteIds)) {
                return new ArrayList<>();
            }
            stringBuilder.append("AND st.siteId IN (").append(siteIds).append(") ");
        }

        // 聚合條件
        stringBuilder.append("GROUP BY st.siteId, st.serviceCode, st.collectionId, UPPER(st.threatName), st.classification ");

        // 排序
        stringBuilder.append("ORDER BY recentTime desc, threatName asc ");

        // 分頁查詢
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getPageSize()) && ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getPageNum())) {
            stringBuilder.append("LIMIT ").append(edrCustomerThreatsParam.getPageSize()).append(" ");
            stringBuilder.append("OFFSET ").append((edrCustomerThreatsParam.getPageNum() - 1) * edrCustomerThreatsParam.getPageSize());
        }

        // 處理參數可能有斜線問題
        String sql = stringBuilder.toString().replace("\\","\\\\");
        List<Map<String, Object>> map = mappingClassificationName(bigDataUtil.srQuery(sql), area);
        String sid = StringUtils.isNotEmpty(edrCustomerThreatsParam.getSid()) ? edrCustomerThreatsParam.getSid() : StringUtil.toString(RequestUtil.getHeaderSid());
        if (ObjectUtils.isNotEmpty(map)) {
            List<Map<String, Object>> processList = edrCustomerV2Mapper.getProcessById(map.stream().map(m -> Objects.toString(m.get("threatName"))).collect(Collectors.toList()), sid);
            map.forEach(m -> {
                String threatName = Objects.toString(m.get("threatName"));
                Map<String, Object> process = processList.stream().filter(p -> Objects.toString(p.get("processCode")).toLowerCase().equals(threatName.toLowerCase())).findAny().orElse(null);
                m.put("processDescription", Objects.isNull(process) ? "" : Objects.toString(process.get("processDescription"), ""));

                String siteId = Objects.toString(m.get("siteId"));
                Map<String, Object> customer = customerOrgList.stream().filter(p -> Objects.toString(p.get("siteId")).equals(siteId)).findAny().orElse(null);
                m.put("customerFullName", Objects.isNull(customer) ? "" : Objects.toString(customer.get("customerFullName"), ""));
                m.put("siteNames", Objects.isNull(customer) ? "" : Objects.toString(customer.get("siteNames"), ""));
                m.put("siteId", Objects.toString(m.get("siteId"), ""));
            });
        }
        return map;
    }
    public BaseResponse getThreatClassificationsCount(EdrCustomerThreatsParam edrCustomerThreatsParam) {
        List<Map<String, Object>> customerOrgList;
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT threats.classification, SUM(threats.count) AS count FROM ( ");
        stringBuilder.append("SELECT st.classification, count(st.threatName) as count FROM servicecloud.SentinelOne_Threats st ");

        // 查詢條件
        buildWhereSql(stringBuilder, edrCustomerThreatsParam);
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getEventStatus()) ||
                ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getIssueCode())) {

            EdrEvent edrEventRequest = new EdrEvent();
            edrEventRequest.setServerId(serverId);
            edrEventRequest.setEventStatus(Objects.toString(edrCustomerThreatsParam.getEventStatus(), ""));
            edrEventRequest.setIssueCode(Objects.toString(edrCustomerThreatsParam.getIssueCode(), ""));
            List<Map<String, Object>> dataList = edrCustomerV2Mapper.getEdrEventList(edrEventRequest);
            List<String> eventIdList = dataList.stream().map(data -> data.get("eventId").toString()).collect(Collectors.toList());
            String eventIds = eventIdList.stream().map(String::valueOf).collect(Collectors.joining(","));

            if (ObjectUtils.isEmpty(edrCustomerThreatsParam.getIssueCode())
                    && Objects.equals(Objects.toString(edrCustomerThreatsParam.getEventStatus(), ""), "unsolved")
                    && StringUtils.isNotEmpty(eventIds)) {
                stringBuilder.append("AND st.id NOT IN (").append(eventIds).append(") ");
            } else {
                if (StringUtils.isEmpty(eventIds)) {
                    BaseResponse.ok();
                }
                stringBuilder.append("AND st.id IN (").append(eventIds).append(") ");
            }
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getSiteNames()) ||
                ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getServiceCode()) ||
                ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getCustomerFullName())) {
            customerOrgList = edrCustomerV2Mapper.getCustomerOrgByServiceCode(edrCustomerThreatsParam.getServiceCode(), edrCustomerThreatsParam.getCustomerFullName(), edrCustomerThreatsParam.getSiteNames());
            String siteIds = customerOrgList.stream().map(data -> Objects.toString(data.get("siteId"), "")).collect(Collectors.joining(","));
            if (StringUtils.isEmpty(siteIds)) {
                BaseResponse.ok();
            }
            stringBuilder.append("AND st.siteId IN (").append(siteIds).append(") ");
        }

        // 聚合條件
        stringBuilder.append("GROUP BY st.siteId, st.serviceCode, st.collectionId, UPPER(st.threatName), st.classification ");
        stringBuilder.append(") threats WHERE threats.classification IS NOT NULL GROUP BY threats.classification ");

        // 處理參數可能有斜線問題
        String sql = stringBuilder.toString().replace("\\","\\\\");
        List<Map<String, Object>> dataList = bigDataUtil.srQuery(sql);

        if (!ObjectUtils.isEmpty(dataList)) {
            dataList = mappingClassificationEnumCode(dataList);
        }

        return BaseResponse.ok(dataList);
    }
    private Integer getThreatsCount(EdrCustomerThreatsParam edrCustomerThreatsParam) {

        // 查詢欄位
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT UPPER(st.threatName) as threatName FROM servicecloud.SentinelOne_Threats st ");

        // 查詢條件
        buildWhereSql(stringBuilder, edrCustomerThreatsParam);
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getEventStatus()) ||
                ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getIssueCode())) {

            EdrEvent edrEventRequest = new EdrEvent();
            edrEventRequest.setServerId(serverId);
            edrEventRequest.setEventStatus(Objects.toString(edrCustomerThreatsParam.getEventStatus(), ""));
            edrEventRequest.setIssueCode(Objects.toString(edrCustomerThreatsParam.getIssueCode(), ""));
            List<Map<String, Object>> dataList = edrCustomerV2Mapper.getEdrEventList(edrEventRequest);
            List<String> eventIdList = dataList.stream().map(data -> data.get("eventId").toString()).collect(Collectors.toList());
            String eventIds = eventIdList.stream().map(String::valueOf).collect(Collectors.joining(","));

            if (ObjectUtils.isEmpty(edrCustomerThreatsParam.getIssueCode())
                    && Objects.equals(Objects.toString(edrCustomerThreatsParam.getEventStatus(), ""), "unsolved")
                    && StringUtils.isNotEmpty(eventIds)) {
                stringBuilder.append("AND st.id NOT IN (").append(eventIds).append(") ");
            } else {
                if (StringUtils.isEmpty(eventIds)) {
                    return 0;
                }
                stringBuilder.append("AND st.id IN (").append(eventIds).append(") ");
            }
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getSiteNames()) ||
                ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getServiceCode()) ||
                ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getCustomerFullName())) {
            List<Map<String, Object>> customerOrgList = edrCustomerV2Mapper.getCustomerOrgByServiceCode(edrCustomerThreatsParam.getServiceCode(), edrCustomerThreatsParam.getCustomerFullName(), edrCustomerThreatsParam.getSiteNames());
            String siteIds = customerOrgList.stream().map(data -> Objects.toString(data.get("siteId"), "")).collect(Collectors.joining(","));
            if (StringUtils.isEmpty(siteIds)) {
                return 0;
            }
            stringBuilder.append("AND st.siteId IN (").append(siteIds).append(") ");
        }

        // 聚合條件
        stringBuilder.append("GROUP BY st.siteId, st.serviceCode, st.collectionId, UPPER(st.threatName), st.classification ");

        // 處理參數可能有斜線問題
        String sql = stringBuilder.toString().replace("\\","\\\\");
        return bigDataUtil.srQuery(sql).size();
    }

    public List<Map<String, Object>> getThreatsDetail(EdrCustomerThreatsParam edrCustomerThreatsParam, String area) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT st.id, st.siteId, st.threatName, st.classification, st.filePath, st.createdAt, ");
        stringBuilder.append("IFNULL(st.lastUpdate, st.updatedAt) updatedAt, st.agentComputerName, st.classificationSource, st.shaCode, ");
        stringBuilder.append("st.agentInfected, st.mitigationStatus, ");
        stringBuilder.append("CASE WHEN EXISTS ( SELECT 1 FROM servicecloud.SentinelOne_Whitelist sw WHERE sw.value = st.shaCode ) ");
        stringBuilder.append("THEN 'release' ELSE 'nonRelease' END AS releaseStatus ");
        stringBuilder.append("FROM servicecloud.SentinelOne_Threats st ");

        // 查詢條件
        buildWhereSql(stringBuilder, edrCustomerThreatsParam);
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getEventStatus())
                || ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getIssueCode())) {
            EdrEvent edrEventRequest = new EdrEvent();
            edrEventRequest.setServerId(serverId);
            edrEventRequest.setEventStatus(Objects.toString(edrCustomerThreatsParam.getEventStatus(), ""));
            edrEventRequest.setIssueCode(Objects.toString(edrCustomerThreatsParam.getIssueCode(), ""));
            List<Map<String, Object>> dataList = edrCustomerV2Mapper.getEdrEventList(edrEventRequest);
            List<String> eventIdList = dataList.stream().map(data -> data.get("eventId").toString()).collect(Collectors.toList());
            String eventIds = eventIdList.stream().map(String::valueOf).collect(Collectors.joining(","));

            if (ObjectUtils.isEmpty(edrCustomerThreatsParam.getIssueCode())
                    && Objects.equals(Objects.toString(edrCustomerThreatsParam.getEventStatus(), ""), "unsolved")
                    && StringUtils.isNotEmpty(eventIds)) {
                stringBuilder.append("AND st.id NOT IN (").append(eventIds).append(") ");
            } else {
                if (StringUtils.isEmpty(eventIds)) {
                    return new ArrayList<>();
                }
                stringBuilder.append("AND st.id IN (").append(eventIds).append(") ");
            }
        }

        // 排序
        stringBuilder.append("ORDER BY st.updatedAt desc, st.id desc, st.threatName asc ");
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getPageSize()) && ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getPageNum())) {
            stringBuilder.append("LIMIT ").append(edrCustomerThreatsParam.getPageSize()).append(" ");
            stringBuilder.append("OFFSET ").append((edrCustomerThreatsParam.getPageNum() - 1) * edrCustomerThreatsParam.getPageSize());
        }

        // 處理參數可能有斜線問題
        String sql = stringBuilder.toString().replace("\\","\\\\");
        List<Map<String, Object>> mapList = mappingClassificationName(bigDataUtil.srQuery(sql), area);
        if (CollectionUtils.isEmpty(mapList)) {
            return Collections.emptyList();
        }

        List<Long> siteIds = mapList.stream().map(item -> LongUtil.objectToLong(item.get("siteId"))).collect(Collectors.toList());
        List<EdrEventDetail> dataList; // 取得有改變事件或放行狀態資料
        List<Map<String, Object>> customerOrgList = edrCustomerV2Mapper.getCustomerOrgById(siteIds); // 取得客戶組織
        Map<String, EdrEventDetail> eventDetailMap; // 取得有改變事件或放行狀態資料-eventId
        List<Map<String, Object>> edrEventInvalidedDetail; // 最後作廢事件詳情

        EdrEvent edrEventRequest = new EdrEvent();
        edrEventRequest.setServerId(serverId);
        List<String> eventIdList = mapList.stream().map(item -> item.get("id").toString()).collect(Collectors.toList());
        edrEventRequest.setEventIdList(eventIdList);
        dataList = edrCustomerV2Mapper.getEdrEventDetailList(edrEventRequest);

        if (CollectionUtils.isEmpty(dataList)) {
            eventDetailMap = Collections.emptyMap();
            edrEventInvalidedDetail = Collections.emptyList();
        } else {
            eventDetailMap = dataList.stream().collect(Collectors.toMap(EdrEventDetail::getEventId, Function.identity(), (a, b) -> a));
            List<String> eventIds = dataList.stream().filter(item -> Objects.equals(Objects.toString(item.getEventStatus(), ""), "invalided")).map(EdrEventDetail::getEventId).collect(Collectors.toList());
            edrEventInvalidedDetail = CollectionUtils.isEmpty(eventIds) ? Collections.emptyList() : edrCustomerV2Mapper.getEdrEventInvalidedDetail(eventIds, serverId);
        }

        // 程序說明
        String sid = StringUtils.isNotEmpty(edrCustomerThreatsParam.getSid()) ? edrCustomerThreatsParam.getSid() : StringUtil.toString(RequestUtil.getHeaderSid());
        List<Map<String, Object>> processList = edrCustomerV2Mapper.getProcessById(mapList.stream().map(m -> Objects.toString(m.get("threatName"))).collect(Collectors.toList()), sid);

        mapList = mapList.stream().map(item -> {
            String eventId = item.get("id").toString();
            EdrEventDetail edrEventDetail = eventDetailMap.get(eventId);
            if (edrEventDetail != null && edrEventDetail.getServerId().equals(serverId)) {
                item.put("eventDescription", Objects.toString(edrEventDetail.getEventDescription(), ""));
                Map<String, Object> findSolvedDetail = edrEventInvalidedDetail.stream()
                        .filter(detail -> Objects.toString(detail.get("eventId")).equals(eventId) &&
                                Objects.toString(detail.get("eventStatus")).equals("solved"))
                        .findFirst()
                        .orElse(null);
                if (Objects.toString(edrEventDetail.getEventStatus(), "").equals("invalided") && // 最後狀態為invalided
                        ObjectUtils.isNotEmpty(findSolvedDetail)) { // 在invalided前一個狀態為solved
                    item.put("eventStatus", "solved");
                    item.put("issueCode", Objects.toString(findSolvedDetail.get("issueCode"), ""));
                } else if (Objects.toString(edrEventDetail.getEventStatus(), "").equals("invalided") && // 最後狀態為invalided
                        ObjectUtils.isEmpty(findSolvedDetail)) { // 在invalided過去狀態為unsolved
                    item.put("eventStatus", "unsolved");
                    item.put("issueCode", "");
                } else {
                    item.put("eventStatus", Objects.toString(edrEventDetail.getEventStatus(), "unsolved"));
                    item.put("issueCode", Objects.toString(edrEventDetail.getIssueCode(), ""));
                }
                item.put("statusRemark", Objects.toString(edrEventDetail.getStatusRemark(), ""));
                item.put("releaseRemark", Objects.toString(edrEventDetail.getReleaseRemark(), ""));
            }

            item.put("eventDescription", item.getOrDefault("eventDescription", ""));
            item.put("eventStatus", item.getOrDefault("eventStatus", "unsolved"));
            item.put("issueCode", item.getOrDefault("issueCode", ""));
            item.put("statusRemark", item.getOrDefault("statusRemark", ""));
            item.put("releaseRemark", item.getOrDefault("releaseRemark", ""));


            Optional<Map<String, Object>> matchingCustomerOrg = customerOrgList.stream()
                    .filter(customerOrg -> customerOrg.get("id").equals(item.get("siteId")))
                    .findFirst();

            matchingCustomerOrg.ifPresent(customerOrg -> item.put("customerOrgName", Objects.toString(customerOrg.get("customerOrgName"), ""))); // 取得客戶組織名稱
            item.remove("siteId");

            String threatName = Objects.toString(item.get("threatName"));
            Map<String, Object> process = processList.stream().filter(p -> Objects.toString(p.get("processCode")).toLowerCase().equals(threatName.toLowerCase())).findAny().orElse(null);
            item.put("processDescription", Objects.isNull(process) ? "" : Objects.toString(process.get("processDescription"), ""));

            return item;
        }).collect(Collectors.toList());

        return mapList;
    }

    private Integer getThreatsDetailCount(EdrCustomerThreatsParam edrCustomerThreatsParam) {

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT 1 FROM servicecloud.SentinelOne_Threats st ");

        // 查詢條件
        buildWhereSql(stringBuilder, edrCustomerThreatsParam);
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getEventStatus()) ||
                ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getIssueCode())) {

            EdrEvent edrEventRequest = new EdrEvent();
            edrEventRequest.setServerId(serverId);
            edrEventRequest.setEventStatus(Objects.toString(edrCustomerThreatsParam.getEventStatus(), ""));
            edrEventRequest.setIssueCode(Objects.toString(edrCustomerThreatsParam.getIssueCode(), ""));
            List<Map<String, Object>> dataList = edrCustomerV2Mapper.getEdrEventList(edrEventRequest);
            List<String> eventIdList = dataList.stream().map(data -> data.get("eventId").toString()).collect(Collectors.toList());
            String eventIds = eventIdList.stream().map(String::valueOf).collect(Collectors.joining(","));

            if (ObjectUtils.isEmpty(edrCustomerThreatsParam.getIssueCode())
                    && Objects.equals(Objects.toString(edrCustomerThreatsParam.getEventStatus(), ""), "unsolved")
                    && StringUtils.isNotEmpty(eventIds)) {
                stringBuilder.append("AND st.id NOT IN (").append(eventIds).append(") ");
            } else {
                if (StringUtils.isEmpty(eventIds)) {
                    return 0;
                }
                stringBuilder.append("AND st.id IN (").append(eventIds).append(") ");
            }
        }
        if (StringUtil.isNotEmpty(edrCustomerThreatsParam.getReleaseStatus())) {
            stringBuilder.append("AND ").append("release".equals(edrCustomerThreatsParam.getReleaseStatus()) ? "EXISTS ( " : "NOT EXISTS ( ");
            stringBuilder.append("SELECT 1 FROM servicecloud.SentinelOne_Whitelist sw WHERE sw.value = st.shaCode ) ");
        }

        // 處理參數可能有斜線問題
        String sql = stringBuilder.toString().replace("\\","\\\\");

        return bigDataUtil.srQuery(sql).size();
    }

    private List<Map<String, Object>> getcollectedTime() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT MAX(collectedTime) as updateTime FROM servicecloud.SentinelOne_Threats ");
        return bigDataUtil.srQuery(stringBuilder.toString());
    }

    private boolean deleteThreads(Long siteId) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("DELETE FROM servicecloud.SentinelOne_Threats ");
        stringBuilder.append("WHERE siteId=").append(siteId).append(" ");
        String sql = stringBuilder.toString();

        JdbcSqlInfo jdbcSqlInfo = new JdbcSqlInfo("starrocks", sql);
        return bigDataUtil.jdbcExecute(jdbcSqlInfo);
    }

    /* 事件狀態轉換
    private static EdrCustomerThreatsParam convertIncidentStatusToCode(EdrCustomerThreatsParam edrCustomerThreatsParam) {
        if (Objects.nonNull(edrCustomerThreatsParam.getIncidentStatus())) {
            switch (edrCustomerThreatsParam.getIncidentStatus()) {
                case "unresolved":
                    edrCustomerThreatsParam.setIncidentStatus("0");
                    break;
                case "resolved":
                    edrCustomerThreatsParam.setIncidentStatus("1");
                    break;
                case "in_progress":
                    edrCustomerThreatsParam.setIncidentStatus("2");
                    break;
            }
        }
        return edrCustomerThreatsParam;
    }

    private static Map<String, Object> convertIncidentStatusToTitle(Map<String, Object> edrCustomerThreat) {
        if (Objects.nonNull(edrCustomerThreat.get("incidentStatus"))) {
            String incidentStatus = StringUtil.toString(edrCustomerThreat.get("incidentStatus"));
            switch (incidentStatus) {
                case "0":
                    edrCustomerThreat.put("incidentStatus", "unresolved");
                    break;
                case "1":
                    edrCustomerThreat.put("incidentStatus", "resolved");
                    break;
                case "2":
                    edrCustomerThreat.put("incidentStatus", "in_progress");
                    break;
            }
        }
        return edrCustomerThreat;
    }
    */

    private static <T> Map<String, Object> convertToMap(T data) {
        Map<String, Object> map = new HashMap<>();
        try {
            Field[] fields = data.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                map.put(field.getName(), field.get(data));
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return map;
    }

    private String mappingClassification(String classification) {
        // 取得分類對應表
        List<EdrCustomerThreatClassification> classificationMappingList = edrCustomerV2Mapper.getClassificationMappingList();

        // 分類對應表轉換為Map，以enumCode為key
        Map<String, String> classificationMap = classificationMappingList.stream()
                .collect(Collectors.toMap(EdrCustomerThreatClassification::getEnumCode, EdrCustomerThreatClassification::getClassification));

        // 字串分割為列表
        List<String> classificationList = Arrays.stream(classification.split(",")).map(String::trim).collect(Collectors.toList());

        // 對應enumCode
        List<String> mappedClassificationList = classificationList.stream()
                .map(classificationMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return String.join("','", mappedClassificationList);
    }

    private List<Map<String, Object>> mappingClassificationName(List<Map<String, Object>> dataList, String area) {
        // 取得分類對應表
        List<EdrCustomerThreatClassification> classificationMappingList = edrCustomerV2Mapper.getClassificationMappingList();

        // 分類對應表轉換為Map，以classification為key
        Map<String, String> classificationMap = classificationMappingList.stream()
                .collect(Collectors.toMap(
                        EdrCustomerThreatClassification::getClassification,
                        "CN".equals(area) ? EdrCustomerThreatClassification::getCnFieldName : EdrCustomerThreatClassification::getTwFieldName
                ));

        // 對應enumCode
        List<Map<String, Object>> mappedClassificationList = dataList.stream()
                .map(data -> {
                    String classification = (String) data.get("classification");
                    if (classification != null && classificationMap.containsKey(classification)) {
                        data.put("classification", classificationMap.get(classification));
                    }
                    return data;
                })
                .collect(Collectors.toList());


        return mappedClassificationList;
    }

    private List<Map<String, Object>> mappingClassificationEnumCode(List<Map<String, Object>> dataList) {
        // 取得分類對應表
        Map<String, String> classificationMappingMap = edrCustomerV2Mapper.getClassificationMappingList().stream()
                .collect(Collectors.toMap(EdrCustomerThreatClassification::getClassification, EdrCustomerThreatClassification::getEnumCode));

        dataList = dataList.stream()
                .map(data -> {
                    String classification = Objects.toString(data.get("classification"), "");
                    if (classificationMappingMap.containsKey(classification)) {
                        data.put("classification", classificationMappingMap.get(classification));
                    }
                    return data;
                }).collect(Collectors.toList());

        return dataList;
    }

    private void syncThreats(Long siteId) {
        try {
            String url = Edrv2Const.SYNC_THREATS_URI;

            // 建立Body數據
            Map<String, Object> requestBody = new HashMap<>();
            List<String> siteIdList = Arrays.asList(StringUtil.toString(siteId));
            requestBody.put("siteIds", siteIdList);
            requestBody.put("env", environment);

            // 将 Map 转换为 JSON 字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonRequestBody = objectMapper.writeValueAsString(requestBody);

            // http請求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(jsonRequestBody, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
        } catch (Exception e) {
            log.error("syncThreats error: ", e);
        }
    }

    private void syncApplication(Long siteId) {
        try {
            String url = Edrv2Const.SYNC_APPLICATION_URI;

            // 建立Body數據
            Map<String, Object> requestBody = new HashMap<>();
            List<String> siteIdList = Arrays.asList(StringUtil.toString(siteId));
            requestBody.put("siteIds", siteIdList);
            requestBody.put("env", environment);

            // 将 Map 转换为 JSON 字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonRequestBody = objectMapper.writeValueAsString(requestBody);

            // http請求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(jsonRequestBody, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
        } catch (Exception e) {
            log.error("syncThreats error: ", e);
        }
    }

    private void syncAgent(Long siteId) {
        try {
            String url = Edrv2Const.SYNC_AGENT_URI;

            // 建立Body數據
            Map<String, Object> requestBody = new HashMap<>();
            List<String> siteIdList = Arrays.asList(StringUtil.toString(siteId));
            requestBody.put("siteIds", siteIdList);
            requestBody.put("env", environment);

            // 将 Map 转换为 JSON 字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonRequestBody = objectMapper.writeValueAsString(requestBody);

            // http請求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(jsonRequestBody, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
        } catch (Exception e) {
            log.error("syncThreats error: ", e);
        }
    }

    // 事件列表匯出excel表頭簡繁體設定
    private List<List<String>> getHeadersBasedOnArea(String area) {
        List<List<String>> headers = new ArrayList<>();
        if ("CN".equals(area)) {
            headers.add(Arrays.asList("程序名称"));
            headers.add(Arrays.asList("事件数量"));
            headers.add(Arrays.asList("威胁类型"));
            headers.add(Arrays.asList("最近发生时间"));
            headers.add(Arrays.asList("程序说明"));
            headers.add(Arrays.asList("事件ID"));
            headers.add(Arrays.asList("应用程序路径"));
            headers.add(Arrays.asList("设备名称"));
            headers.add(Arrays.asList("触发来源"));
            headers.add(Arrays.asList("首次发生时间"));
            headers.add(Arrays.asList("最近发生时间"));
            headers.add(Arrays.asList("事件说明"));
            headers.add(Arrays.asList("事件状态"));
            headers.add(Arrays.asList("放行状态"));
            headers.add(Arrays.asList("最近案件代号"));
        } else {
            headers.add(Arrays.asList("程序名稱"));
            headers.add(Arrays.asList("事件數量"));
            headers.add(Arrays.asList("威脅類型"));
            headers.add(Arrays.asList("最近發生時間"));
            headers.add(Arrays.asList("程序說明"));
            headers.add(Arrays.asList("事件ID"));
            headers.add(Arrays.asList("應用程序路徑"));
            headers.add(Arrays.asList("設備名稱"));
            headers.add(Arrays.asList("觸發來源"));
            headers.add(Arrays.asList("首次發生時間"));
            headers.add(Arrays.asList("最近發生時間"));
            headers.add(Arrays.asList("事件說明"));
            headers.add(Arrays.asList("事件狀態"));
            headers.add(Arrays.asList("放行狀態"));
            headers.add(Arrays.asList("最近案件代號"));
        }
        return headers;
    }

    private void buildWhereSql(StringBuilder sb, EdrCustomerThreatsParam edrCustomerThreatsParam) {
        sb.append("WHERE 1=1 ");
        if (LongUtil.isNotEmpty(edrCustomerThreatsParam.getEid())) {
            sb.append("AND st.eid=").append(edrCustomerThreatsParam.getEid()).append(" ");
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getAgentComputerName())) {
            sb.append("AND LOWER(st.agentComputerName) LIKE LOWER('%").append(edrCustomerThreatsParam.getAgentComputerName()).append("%') ");
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getAgentMachineType())) {
            sb.append("AND LOWER(st.agentMachineType) LIKE LOWER('%").append(edrCustomerThreatsParam.getAgentMachineType()).append("%') ");
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getThreatTimeFrom())) {
            String timeFrom = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(edrCustomerThreatsParam.getThreatTimeFrom());
            sb.append("AND CAST(IFNULL(st.lastUpdate, st.updatedAt) AS VARCHAR) >= '").append(timeFrom).append("' ");
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getThreatTimeTo())) {
            LocalDate date = edrCustomerThreatsParam.getThreatTimeTo().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDateTime dateTime = date.atTime(23, 59, 59);
            String timeTo = dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            sb.append("AND CAST(IFNULL(st.lastUpdate, st.updatedAt) AS VARCHAR) <= '").append(timeTo).append("' ");
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getThreatId())) {
            sb.append("AND st.id IN (").append(edrCustomerThreatsParam.getThreatId()).append(") ");
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getClassification())) {
            sb.append("AND st.classification IN ('").append(mappingClassification(edrCustomerThreatsParam.getClassification())).append("') ");
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getClassificationSource())) {
            sb.append("AND LOWER(st.classificationSource) LIKE LOWER('%").append(edrCustomerThreatsParam.getClassificationSource()).append("%') ");
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getSiteIds())) {
            sb.append("AND st.siteId IN (").append(edrCustomerThreatsParam.getSiteIds()).append(") ");
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getThreatName())) {
            sb.append("AND LOWER(st.threatName) LIKE LOWER('%").append(edrCustomerThreatsParam.getThreatName()).append("%') ");
        }
        if (ObjectUtils.isNotEmpty(edrCustomerThreatsParam.getServiceCode())) {
            sb.append("AND st.serviceCode='").append(edrCustomerThreatsParam.getServiceCode()).append("' ");
        }
        if (StringUtil.isNotEmpty(edrCustomerThreatsParam.getCollectionId())) {
            sb.append("AND st.collectionId=").append(edrCustomerThreatsParam.getCollectionId()).append(" ");
        }
        if (StringUtil.isNotEmpty(edrCustomerThreatsParam.getReleaseStatus())) {
            sb.append("AND ").append(RELEASE.equalsIgnoreCase(edrCustomerThreatsParam.getReleaseStatus()) ? "EXISTS ( " : "NOT EXISTS ( ");
            sb.append("SELECT 1 FROM servicecloud.SentinelOne_Whitelist sw WHERE sw.value = st.shaCode ) ");
        }
    }
}
