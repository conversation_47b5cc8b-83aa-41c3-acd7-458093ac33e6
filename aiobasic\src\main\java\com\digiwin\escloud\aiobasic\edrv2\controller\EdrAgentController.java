package com.digiwin.escloud.aiobasic.edrv2.controller;

import com.digiwin.escloud.aiobasic.edrv2.model.*;
import com.digiwin.escloud.aiobasic.report.model.EdrAgentField;
import com.digiwin.escloud.aiobasic.edrv2.service.IEdrAgentService;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Api(value = "/edr/v2/agent", protocols = "HTTP", tags = {"edrv2設備相關接口"}, description = "edrv2設備相關接口")
@Slf4j
@RestController
@RequestMapping("/edr/v2/agent")
public class EdrAgentController {
    @Autowired
    IEdrAgentService edrAgentService;

    @ApiOperation(value = "查詢端點防護設備清單")
    @PostMapping("/list")
    public BaseResponse getAgentList(@RequestParam(value = "eids") List<String> eidList,
                                     @RequestBody EdrAgentParam edrAgentParam) {
        try {
            return edrAgentService.getAgentList(eidList, edrAgentParam);
        } catch (Exception e) {
            log.error("EDRv2 Get Agent List Failed: ", e);
            return BaseResponse.error(ResponseCode.EDR_GET_AGENT_FAILED);
        }
    }

    @ApiOperation(value = "保存設備")
    @PostMapping(value = "/save")
    public BaseResponse saveAgent(@RequestBody List<EdrAgentField> edrAgentFieldRequest) {
        try {
            return edrAgentService.saveAgent(edrAgentFieldRequest);
        } catch (Exception e) {
            log.error("EDRv2 Save Agent Failed: ", e);
            return BaseResponse.error(ResponseCode.EDR_SAVE_AGENT_FAILED);
        }
    }

    @ApiOperation(value = "查詢作業系統類型統計")
    @GetMapping("/os/totalAmount")
    public BaseResponse getOsTotalAmount(@RequestParam(value = "eid") String eid,
                                         @RequestParam(required = false, value = "timeFrom") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date timeFrom,
                                         @RequestParam(required = false, value = "timeTo") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date timeTo) {
        try {
            return edrAgentService.getOsTotalAmount(eid, timeFrom, timeTo);
        } catch (Exception e) {
            log.error("EDRv2 Get Os TotalAmount Failed: ", e);
            return BaseResponse.error(ResponseCode.EDR_GET_AGENT_OS_TOTAL_AMOUNT_FAILED);
        }
    }

    @ApiOperation(value = "啟用/關閉Agent")
    @PostMapping("/{state}")
    public BaseResponse enableOrDisableAgent(@PathVariable("state") String state,
                                             @RequestBody EdrAgentEnableParam edrAgentEnableParam) {
        try {
            return edrAgentService.enableOrDisableAgent(state, edrAgentEnableParam);
        } catch (Exception e) {
            log.error("EDRv2 Enable Or Disable Failed: ", e);
            return BaseResponse.error(ResponseCode.EDR_AGENT_ENABLE_OR_DISABLE_FAILED);
        }
    }

    @ApiOperation(value = "設備連線/離線數")
    @GetMapping("/connectivityTotalAmount")
    public BaseResponse getConnectivityTotalAmount(@RequestParam(value = "eid") Long eid,
                                         @RequestParam(required = false, value = "timeFrom") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date timeFrom,
                                         @RequestParam(required = false, value = "timeTo") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date timeTo) {
        try {
            return edrAgentService.getConnectivityTotalAmount(eid, timeFrom, timeTo);
        } catch (Exception e) {
            log.error("EDRv2 Failed", e);
            return BaseResponse.error(ResponseCode.EDR_GET_CONNECTION_TOTAL_AMOUNT_FAILED);
        }
    }

    @ApiOperation(value = "設備健康/不健康")
    @GetMapping("/healthTotalAmount")
    public BaseResponse getHealthTotalAmount(@RequestParam(value = "eid") Long eid,
                                         @RequestParam(required = false, value = "timeFrom") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date timeFrom,
                                         @RequestParam(required = false, value = "timeTo") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date timeTo) {
        try {
            return edrAgentService.getHealthTotalAmount(eid, timeFrom, timeTo);
        } catch (Exception e) {
            log.error("EDRv2 Failed", e);
            return BaseResponse.error(ResponseCode.EDR_GET_AGENT_HEALTH_TOTAL_AMOUNT_FAILED);
        }
    }

    @ApiOperation(value = "組件運行狀態")
    @GetMapping("/AgentConnectivityTotalAmount")
    public BaseResponse getAgentConnectivityTotalAmount(@RequestParam(value = "eid") Long eid,
                                         @RequestParam(required = false, value = "timeFrom") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date timeFrom,
                                         @RequestParam(required = false, value = "timeTo") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date timeTo) {
        try {
            return edrAgentService.getAgentConnectivityTotalAmount(eid, timeFrom, timeTo);
        } catch (Exception e) {
            log.error("EDRv2 Failed", e);
            return BaseResponse.error(ResponseCode.EDR_GET_AGENT_CONNECTION_TOTAL_AMOUNT_FAILED);
        }
    }

    @ApiOperation(value = "刪除設備")
    @DeleteMapping("/remove")
    public BaseResponse removeAgent(@RequestBody EdrAgentDeleteParam param) {
        try {
            return edrAgentService.removeAgent(param);
        } catch (Exception ex) {
            log.error("EDRv2 Remove Agent Failed: ", ex);
            return BaseResponse.error(ResponseCode.EDR_AGENT_REMOVE_FAILED);
        }
    }

    @ApiOperation(value = "作業系統清單")
    @GetMapping("/os/list")
    public BaseResponse getOsName(@RequestParam(required = false) Long eid) {
        try {
            return edrAgentService.getOsNameList(eid);
        } catch (Exception ex) {
            log.error("EDRv2 Get Agent OS List Failed: ", ex);
            return BaseResponse.error(ResponseCode.EDR_GET_AGENT_OS_LIST_FAILED);
        }
    }

    @ApiOperation(value = "設備列表即時同步")
    @PostMapping(value = "/sync")
    public BaseResponse syncEvent(@RequestParam String eid,
                                  @RequestBody List<String> siteIds) {
        try {
            return edrAgentService.syncAgent(eid, siteIds);
        } catch (Exception e) {
            log.error("EDRv2 Syncing agent Failed: ", e);
            return BaseResponse.error(ResponseCode.EDR_SYNC_AGENT_FAILED);
        }
    }

    @ApiOperation(value = "設備狀態檢查")
    @PostMapping(value = "/status")
    public BaseResponse checkAgentStatus(@RequestBody EdrAgentCheckStatusParam param) {
        try {
            return edrAgentService.checkAgentStatus(param);
        } catch (Exception ex) {
            log.error("EDRv2 Check Agent Status Failed", ex);
            return BaseResponse.error(ResponseCode.EDR_AGENT_CHECK_STATUS_FAILED);
        }
    }

    @ApiOperation(value = "設備匯出")
    @PostMapping(value = "/export")
    public BaseResponse exportAgentList(HttpServletResponse response,
                                    @RequestParam String eid,
                                    @RequestBody EdrAgentExportParam param) {
        try {
            edrAgentService.exportAgentList(response, eid, param);
            return BaseResponse.ok();
        } catch (Exception ex) {
            log.error("EDRv2 Export Agent List Failed", ex);
            return BaseResponse.error(ResponseCode.INTERNAL_ERROR);
        }
    }

    @ApiOperation(value = "全機掃描")
    @PostMapping (value = "/scan")
    public BaseResponse scan(@RequestBody EdrScan edrScan) {
        try {
            return edrAgentService.scan(edrScan);
        } catch (Exception e) {
            log.error("EDRv2 Scan Failed: ", e);
            return BaseResponse.dynamicError(ResponseCode.EDR_SCAN_FAILED, e);
        }
    }

    @ApiOperation(value = "網路隔離與解除")
    @PostMapping("/network")
    public BaseResponse networkIsolationOrConnect(@RequestBody EdrNetworkIsolationParam param) {
        return  edrAgentService.networkIsolationOrConnect(param);
    }

    @ApiOperation(value = "移除組件驗證郵件")
    @PostMapping (value = "/identity/send")
    public BaseResponse sendRemoveIdentity(@RequestBody EdrIdentitySend edrIdentitySend) {
        try {
            return edrAgentService.sendRemoveIdentity(edrIdentitySend);
        } catch (Exception e) {
            log.error("EDRv2 Identity Send Failed: ", e);
            return BaseResponse.dynamicError(ResponseCode.EDR_IDENTITY_SEND_FAILED, e);
        }
    }

    @ApiOperation(value = "移除組件")
    @PostMapping (value = "/uninstall")
    public BaseResponse uninstall(@RequestBody EdrUninstall edrUninstall) {
        try {
            return edrAgentService.uninstall(edrUninstall);
        } catch (Exception e) {
            log.error("EDRv2 Uninstall Failed: ", e);
            return BaseResponse.dynamicError(ResponseCode.EDR_UNINSTALL_FAILED, e);
        }
    }

    @ApiOperation(value = "批量變更設備否預警")
    @PostMapping(value = "/isWarning")
    public BaseResponse saveIsWarning(@RequestBody EdrAgentIsWarningParam param) {
        try {
            return edrAgentService.saveIsWarning(param);
        } catch (Exception ex) {
            log.error("EDRv2 Save isWarning Failed", ex);
            return BaseResponse.error(ResponseCode.EDR_AGENT_SAVE_ISWARNING_ERROR);
        }
    }

    @ApiOperation(value = "查詢設備是否預警")
    @GetMapping(value = "/isWarning")
    public BaseResponse getIsWaning(@RequestParam(required = false, value = "siteId") String siteId,
                                    @RequestParam(required = false, value = "isWarning") Boolean isWarning) {
        try {
            return edrAgentService.selectIsWarning(siteId, isWarning);
        } catch (Exception ex) {
            log.error("EDRv2 Get isWarning Failed", ex);
            return BaseResponse.error(ResponseCode.EDR_AGENT_GET_ISWARNING_ERROR);
        }
    }
}
